using System.ComponentModel.DataAnnotations;

namespace CareerPortal.Data
{
    public class Vacancy
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string? Title { get; set; }

        [Required]
        public string? Description { get; set; }

        [Required]
        public string? Responsibilities { get; set; }

        [Required]
        public string? Qualifications { get; set; }

        [Required]
        [StringLength(50)]
        public string? Location { get; set; }

        [Required]
        [StringLength(50)]
        public string? Department { get; set; }

        public DateTime PostedDate { get; set; } = DateTime.UtcNow;

        public DateTime? ClosingDate { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation property for applications
        public ICollection<CandidateApplication>? Applications { get; set; }
    }
}