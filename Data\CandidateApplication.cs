using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CareerPortal.Data
{
    public class CandidateApplication
    {
        public int Id { get; set; }

        [Required]
        public int CandidateProfileId { get; set; }
        [ForeignKey("CandidateProfileId")]
        public CandidateProfile? CandidateProfile { get; set; }

        [Required]
        public int VacancyId { get; set; }
        [ForeignKey("VacancyId")]
        public Vacancy? Vacancy { get; set; }

        public DateTime ApplicationDate { get; set; } = DateTime.UtcNow;

        // Optional: Store the CoverLetter Id used for this application
        public int? CoverLetterId { get; set; }
        [ForeignKey("CoverLetterId")]
        public CoverLetter? CoverLetter { get; set; }

        // Optional: Store the CV path or content used for this application
        public string? CvPathAtApplication { get; set; } // Or byte[] CvContentAtApplication

        [StringLength(50)]
        public string? Status { get; set; } // e.g., Applied, Under Review, Shortlisted, Rejected, Hired
    }
}