{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "CareerPortal.modules.json", "AssetFile": "CareerPortal.modules.json.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.010416666667"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "95"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"tMpc4Mp5Kart9mS3UELWfInMDPFZYORmj0wiAw7JwBs=\""}, {"Name": "ETag", "Value": "W/\"sgZOtYFoQbtT5NzEZj6mvNpqny3TPXduyD8nEg1wGig=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sgZOtYFoQbtT5NzEZj6mvNpqny3TPXduyD8nEg1wGig="}]}, {"Route": "CareerPortal.modules.json", "AssetFile": "CareerPortal.modules.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "114"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"sgZOtYFoQbtT5NzEZj6mvNpqny3TPXduyD8nEg1wGig=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:55:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sgZOtYFoQbtT5NzEZj6mvNpqny3TPXduyD8nEg1wGig="}]}, {"Route": "CareerPortal.modules.json.gz", "AssetFile": "CareerPortal.modules.json.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "95"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"tMpc4Mp5Kart9mS3UELWfInMDPFZYORmj0wiAw7JwBs=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tMpc4Mp5Kart9mS3UELWfInMDPFZYORmj0wiAw7JwBs="}]}, {"Route": "CareerPortal.styles.css", "AssetFile": "CareerPortal.styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.008849557522"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "112"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Qjl948eSnEILonn3wELtC6BV9k8NVi6yotYo3mJkPSk=\""}, {"Name": "ETag", "Value": "W/\"Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Link", "Value": "<_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css>; rel=\"preload\"; as=\"style\""}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk="}]}, {"Route": "CareerPortal.styles.css", "AssetFile": "CareerPortal.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "131"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:55:26 GMT"}, {"Name": "Link", "Value": "<_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk="}]}, {"Route": "CareerPortal.styles.css.gz", "AssetFile": "CareerPortal.styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "112"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Qjl948eSnEILonn3wELtC6BV9k8NVi6yotYo3mJkPSk=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Qjl948eSnEILonn3wELtC6BV9k8NVi6yotYo3mJkPSk="}]}, {"Route": "CareerPortal.tnv30r1bl8.styles.css", "AssetFile": "CareerPortal.styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.008849557522"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "112"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Qjl948eSnEILonn3wELtC6BV9k8NVi6yotYo3mJkPSk=\""}, {"Name": "ETag", "Value": "W/\"Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Link", "Value": "<_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css>; rel=\"preload\"; as=\"style\""}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tnv30r1bl8"}, {"Name": "integrity", "Value": "sha256-Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk="}, {"Name": "label", "Value": "CareerPortal.styles.css"}]}, {"Route": "CareerPortal.tnv30r1bl8.styles.css", "AssetFile": "CareerPortal.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "131"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:55:26 GMT"}, {"Name": "Link", "Value": "<_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tnv30r1bl8"}, {"Name": "integrity", "Value": "sha256-Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk="}, {"Name": "label", "Value": "CareerPortal.styles.css"}]}, {"Route": "CareerPortal.tnv30r1bl8.styles.css.gz", "AssetFile": "CareerPortal.styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "112"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Qjl948eSnEILonn3wELtC6BV9k8NVi6yotYo3mJkPSk=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tnv30r1bl8"}, {"Name": "integrity", "Value": "sha256-Qjl948eSnEILonn3wELtC6BV9k8NVi6yotYo3mJkPSk="}, {"Name": "label", "Value": "CareerPortal.styles.css.gz"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Anchor/FluentAnchor.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Anchor/FluentAnchor.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "430"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"b9RSPukLvSHekr3kftcukF9Hbr4g1a5l0/cfyJ61XMA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b9RSPukLvSHekr3kftcukF9Hbr4g1a5l0/cfyJ61XMA="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Anchor/FluentAnchor.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Anchor/FluentAnchor.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003731343284"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "267"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mTB1BoNq7t/us6gvhhRKI480CaK7vT9E+t+AL9an8wU=\""}, {"Name": "ETag", "Value": "W/\"b9RSPukLvSHekr3kftcukF9Hbr4g1a5l0/cfyJ61XMA=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b9RSPukLvSHekr3kftcukF9Hbr4g1a5l0/cfyJ61XMA="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Anchor/FluentAnchor.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Anchor/FluentAnchor.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "267"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mTB1BoNq7t/us6gvhhRKI480CaK7vT9E+t+AL9an8wU=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mTB1BoNq7t/us6gvhhRKI480CaK7vT9E+t+AL9an8wU="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/AnchoredRegion/FluentAnchoredRegion.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/AnchoredRegion/FluentAnchoredRegion.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3215"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RC8P8iQcUu/cx7osATv0xSw2X8G3utkMtThwigsND5g=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RC8P8iQcUu/cx7osATv0xSw2X8G3utkMtThwigsND5g="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/AnchoredRegion/FluentAnchoredRegion.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/AnchoredRegion/FluentAnchoredRegion.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001003009027"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "996"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pIZWamBcM95RyKOecGwL1QrEdg/IKc5rscB4MFCIujM=\""}, {"Name": "ETag", "Value": "W/\"RC8P8iQcUu/cx7osATv0xSw2X8G3utkMtThwigsND5g=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RC8P8iQcUu/cx7osATv0xSw2X8G3utkMtThwigsND5g="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/AnchoredRegion/FluentAnchoredRegion.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/AnchoredRegion/FluentAnchoredRegion.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "996"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pIZWamBcM95RyKOecGwL1QrEdg/IKc5rscB4MFCIujM=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pIZWamBcM95RyKOecGwL1QrEdg/IKc5rscB4MFCIujM="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Button/FluentButton.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Button/FluentButton.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "522"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8QTQtCTbbHkwqt3rAy8ZPjez2lZ6PGmR5Il+7Q3g/rs=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8QTQtCTbbHkwqt3rAy8ZPjez2lZ6PGmR5Il+7Q3g/rs="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Button/FluentButton.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Button/FluentButton.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003164556962"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "315"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RbdcZZJvJ98rtWAD8Wl13T6Jd/A0WUJIyozbvbTNME8=\""}, {"Name": "ETag", "Value": "W/\"8QTQtCTbbHkwqt3rAy8ZPjez2lZ6PGmR5Il+7Q3g/rs=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8QTQtCTbbHkwqt3rAy8ZPjez2lZ6PGmR5Il+7Q3g/rs="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Button/FluentButton.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Button/FluentButton.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "315"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RbdcZZJvJ98rtWAD8Wl13T6Jd/A0WUJIyozbvbTNME8=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RbdcZZJvJ98rtWAD8Wl13T6Jd/A0WUJIyozbvbTNME8="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Checkbox/FluentCheckbox.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Checkbox/FluentCheckbox.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "368"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gVrV4WI8finQdUGG7EIZIAh2tTbFW0GF7Hl73l/1JnE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gVrV4WI8finQdUGG7EIZIAh2tTbFW0GF7Hl73l/1JnE="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Checkbox/FluentCheckbox.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Checkbox/FluentCheckbox.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.004444444444"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "224"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pWpjnsF9tEf741/+XNZJIUp59j8Fqiqzv5qPR9T/F/k=\""}, {"Name": "ETag", "Value": "W/\"gVrV4WI8finQdUGG7EIZIAh2tTbFW0GF7Hl73l/1JnE=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gVrV4WI8finQdUGG7EIZIAh2tTbFW0GF7Hl73l/1JnE="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Checkbox/FluentCheckbox.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Checkbox/FluentCheckbox.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "224"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pWpjnsF9tEf741/+XNZJIUp59j8Fqiqzv5qPR9T/F/k=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pWpjnsF9tEf741/+XNZJIUp59j8Fqiqzv5qPR9T/F/k="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DataGrid/FluentDataGrid.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DataGrid/FluentDataGrid.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14173"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"PbscDKK1vxqZeEv6a+4x9gKVhqxHVYgy9iE0fMp2iDY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PbscDKK1vxqZeEv6a+4x9gKVhqxHVYgy9iE0fMp2iDY="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DataGrid/FluentDataGrid.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DataGrid/FluentDataGrid.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000339904827"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2941"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UQRUr2KMtLUqt9ogdiIWMXFjGLFL3UBvDZCD4pGFuKQ=\""}, {"Name": "ETag", "Value": "W/\"PbscDKK1vxqZeEv6a+4x9gKVhqxHVYgy9iE0fMp2iDY=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PbscDKK1vxqZeEv6a+4x9gKVhqxHVYgy9iE0fMp2iDY="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DataGrid/FluentDataGrid.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DataGrid/FluentDataGrid.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2941"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UQRUr2KMtLUqt9ogdiIWMXFjGLFL3UBvDZCD4pGFuKQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UQRUr2KMtLUqt9ogdiIWMXFjGLFL3UBvDZCD4pGFuKQ="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DesignSystemProvider/FluentDesignTheme.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DesignSystemProvider/FluentDesignTheme.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2058"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"abaSp2xCB2qa0FHuc/9VKt39+MybEnIoOItPufNTSr4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-abaSp2xCB2qa0FHuc/9VKt39+MybEnIoOItPufNTSr4="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DesignSystemProvider/FluentDesignTheme.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DesignSystemProvider/FluentDesignTheme.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001329787234"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "751"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ebidgEF0qxv0FVWWHIu9oAwtiTaKJTgfFFW7oNulicU=\""}, {"Name": "ETag", "Value": "W/\"abaSp2xCB2qa0FHuc/9VKt39+MybEnIoOItPufNTSr4=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-abaSp2xCB2qa0FHuc/9VKt39+MybEnIoOItPufNTSr4="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DesignSystemProvider/FluentDesignTheme.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DesignSystemProvider/FluentDesignTheme.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "751"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ebidgEF0qxv0FVWWHIu9oAwtiTaKJTgfFFW7oNulicU=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ebidgEF0qxv0FVWWHIu9oAwtiTaKJTgfFFW7oNulicU="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Divider/FluentDivider.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Divider/FluentDivider.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "388"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CndcCP/YVXs68LoE68COc38ypIJenMbJyu+fR0/ZIPc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CndcCP/YVXs68LoE68COc38ypIJenMbJyu+fR0/ZIPc="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Divider/FluentDivider.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Divider/FluentDivider.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.004273504274"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "233"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"E+0P+4Q5g9SzSGdE20j3R0nckgPdNoGc0x+TGJ/TIYI=\""}, {"Name": "ETag", "Value": "W/\"CndcCP/YVXs68LoE68COc38ypIJenMbJyu+fR0/ZIPc=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CndcCP/YVXs68LoE68COc38ypIJenMbJyu+fR0/ZIPc="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Divider/FluentDivider.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Divider/FluentDivider.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "233"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"E+0P+4Q5g9SzSGdE20j3R0nckgPdNoGc0x+TGJ/TIYI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-E+0P+4Q5g9SzSGdE20j3R0nckgPdNoGc0x+TGJ/TIYI="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Grid/FluentGrid.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Grid/FluentGrid.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3005"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"V4iZz/kay7SoC/eRuDViVZkhxiL1oNW1gzMAFC6k/wY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V4iZz/kay7SoC/eRuDViVZkhxiL1oNW1gzMAFC6k/wY="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Grid/FluentGrid.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Grid/FluentGrid.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001321003963"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "756"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QySeeEbzUPczOjYjev2fIc86i78Pib7zlTPyfCkLv+8=\""}, {"Name": "ETag", "Value": "W/\"V4iZz/kay7SoC/eRuDViVZkhxiL1oNW1gzMAFC6k/wY=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V4iZz/kay7SoC/eRuDViVZkhxiL1oNW1gzMAFC6k/wY="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Grid/FluentGrid.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Grid/FluentGrid.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "756"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QySeeEbzUPczOjYjev2fIc86i78Pib7zlTPyfCkLv+8=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QySeeEbzUPczOjYjev2fIc86i78Pib7zlTPyfCkLv+8="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/HorizontalScroll/FluentHorizontalScroll.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/HorizontalScroll/FluentHorizontalScroll.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "348"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yf+15AR63QV4X8XvrAMxrEP5sX3Ea0tuh+Tsinb6yXU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yf+15AR63QV4X8XvrAMxrEP5sX3Ea0tuh+Tsinb6yXU="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/HorizontalScroll/FluentHorizontalScroll.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/HorizontalScroll/FluentHorizontalScroll.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.007042253521"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "141"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"2oCN2IEWRZPjloAzwScHmDLBlebfEgP366812p/E2c8=\""}, {"Name": "ETag", "Value": "W/\"yf+15AR63QV4X8XvrAMxrEP5sX3Ea0tuh+Tsinb6yXU=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yf+15AR63QV4X8XvrAMxrEP5sX3Ea0tuh+Tsinb6yXU="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/HorizontalScroll/FluentHorizontalScroll.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/HorizontalScroll/FluentHorizontalScroll.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "141"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"2oCN2IEWRZPjloAzwScHmDLBlebfEgP366812p/E2c8=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2oCN2IEWRZPjloAzwScHmDLBlebfEgP366812p/E2c8="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/InputFile/FluentInputFile.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/InputFile/FluentInputFile.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2813"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"m9D6O5smUPMQWbjax0bH03XYtdI3RD5geOwhizeT+gE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m9D6O5smUPMQWbjax0bH03XYtdI3RD5geOwhizeT+gE="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/InputFile/FluentInputFile.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/InputFile/FluentInputFile.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001199040767"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "833"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"y0GyYNSHHrdFmkoHrD7jQi0oehiE/vh7Pi7iAEYeorU=\""}, {"Name": "ETag", "Value": "W/\"m9D6O5smUPMQWbjax0bH03XYtdI3RD5geOwhizeT+gE=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m9D6O5smUPMQWbjax0bH03XYtdI3RD5geOwhizeT+gE="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/InputFile/FluentInputFile.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/InputFile/FluentInputFile.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "833"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"y0GyYNSHHrdFmkoHrD7jQi0oehiE/vh7Pi7iAEYeorU=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-y0GyYNSHHrdFmkoHrD7jQi0oehiE/vh7Pi7iAEYeorU="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/KeyCode/FluentKeyCode.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/KeyCode/FluentKeyCode.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3477"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3+jF/yOfwYyQhLujhQlSrvp3NBll+oEUF7v13pin53A=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3+jF/yOfwYyQhLujhQlSrvp3NBll+oEUF7v13pin53A="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/KeyCode/FluentKeyCode.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/KeyCode/FluentKeyCode.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001124859393"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "888"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"TVs9bq3kylVYMAlXR3Kt4NTTdLquoTIUYrtItz80G30=\""}, {"Name": "ETag", "Value": "W/\"3+jF/yOfwYyQhLujhQlSrvp3NBll+oEUF7v13pin53A=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3+jF/yOfwYyQhLujhQlSrvp3NBll+oEUF7v13pin53A="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/KeyCode/FluentKeyCode.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/KeyCode/FluentKeyCode.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "888"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"TVs9bq3kylVYMAlXR3Kt4NTTdLquoTIUYrtItz80G30=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TVs9bq3kylVYMAlXR3Kt4NTTdLquoTIUYrtItz80G30="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Label/FluentInputLabel.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Label/FluentInputLabel.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "473"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hXPNDHD1hTdz/sH1cD60f/ehIklf8zQAEE73UZNGtu8=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hXPNDHD1hTdz/sH1cD60f/ehIklf8zQAEE73UZNGtu8="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Label/FluentInputLabel.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Label/FluentInputLabel.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003937007874"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "253"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xT1lGfZ95NQfS99lKqhohgyvrwO4H0pOZ0PZuIj2+Xo=\""}, {"Name": "ETag", "Value": "W/\"hXPNDHD1hTdz/sH1cD60f/ehIklf8zQAEE73UZNGtu8=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hXPNDHD1hTdz/sH1cD60f/ehIklf8zQAEE73UZNGtu8="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Label/FluentInputLabel.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Label/FluentInputLabel.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "253"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xT1lGfZ95NQfS99lKqhohgyvrwO4H0pOZ0PZuIj2+Xo=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xT1lGfZ95NQfS99lKqhohgyvrwO4H0pOZ0PZuIj2+Xo="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentAutocomplete.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentAutocomplete.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1299"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"2bhET+uXWbAao2aJyUqqscx9PObMTXmpUAkDQOQBGI8=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2bhET+uXWbAao2aJyUqqscx9PObMTXmpUAkDQOQBGI8="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentAutocomplete.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentAutocomplete.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001748251748"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "571"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YtlDojVlcrK2+2QBRP4LPueVRl8gCsL6Ez4wknxUJLQ=\""}, {"Name": "ETag", "Value": "W/\"2bhET+uXWbAao2aJyUqqscx9PObMTXmpUAkDQOQBGI8=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2bhET+uXWbAao2aJyUqqscx9PObMTXmpUAkDQOQBGI8="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentAutocomplete.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentAutocomplete.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "571"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YtlDojVlcrK2+2QBRP4LPueVRl8gCsL6Ez4wknxUJLQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YtlDojVlcrK2+2QBRP4LPueVRl8gCsL6Ez4wknxUJLQ="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentCombobox.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentCombobox.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1483"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OqLCO17dCq/aFFg8O0mXN/fF4czXAd6R+vgnYjtdPwc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OqLCO17dCq/aFFg8O0mXN/fF4czXAd6R+vgnYjtdPwc="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentCombobox.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentCombobox.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001845018450"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "541"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4fE5RW6001fSJc6+Ju/+RSpNSo5ZsvCuJri+aL4wof8=\""}, {"Name": "ETag", "Value": "W/\"OqLCO17dCq/aFFg8O0mXN/fF4czXAd6R+vgnYjtdPwc=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OqLCO17dCq/aFFg8O0mXN/fF4czXAd6R+vgnYjtdPwc="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentCombobox.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentCombobox.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "541"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4fE5RW6001fSJc6+Ju/+RSpNSo5ZsvCuJri+aL4wof8=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4fE5RW6001fSJc6+Ju/+RSpNSo5ZsvCuJri+aL4wof8="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/ListComponentBase.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/ListComponentBase.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "177"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/lFyXHGb/lh02BDFUuMzwbfU+zNOdnw2s2zKSrTtW00=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/lFyXHGb/lh02BDFUuMzwbfU+zNOdnw2s2zKSrTtW00="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/ListComponentBase.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/ListComponentBase.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.007246376812"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "137"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kD64FhYDGF1SA7wVn4LL4nX1pTWXmxuMYqo2xXip44E=\""}, {"Name": "ETag", "Value": "W/\"/lFyXHGb/lh02BDFUuMzwbfU+zNOdnw2s2zKSrTtW00=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/lFyXHGb/lh02BDFUuMzwbfU+zNOdnw2s2zKSrTtW00="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/ListComponentBase.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/ListComponentBase.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "137"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kD64FhYDGF1SA7wVn4LL4nX1pTWXmxuMYqo2xXip44E=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kD64FhYDGF1SA7wVn4LL4nX1pTWXmxuMYqo2xXip44E="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Menu/FluentMenu.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Menu/FluentMenu.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "725"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"C/YKywsVlWaSpZ1PLDeRKkkkM6ki2G2gT9ny+WVuERA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-C/YKywsVlWaSpZ1PLDeRKkkkM6ki2G2gT9ny+WVuERA="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Menu/FluentMenu.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Menu/FluentMenu.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003344481605"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "298"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8zrNWZqy7raoxnB2CdCgVMkZDFcG6ue/1K4DaARm2RI=\""}, {"Name": "ETag", "Value": "W/\"C/YKywsVlWaSpZ1PLDeRKkkkM6ki2G2gT9ny+WVuERA=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-C/YKywsVlWaSpZ1PLDeRKkkkM6ki2G2gT9ny+WVuERA="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Menu/FluentMenu.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Menu/FluentMenu.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "298"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8zrNWZqy7raoxnB2CdCgVMkZDFcG6ue/1K4DaARm2RI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8zrNWZqy7raoxnB2CdCgVMkZDFcG6ue/1K4DaARm2RI="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/NavMenu/FluentNavMenu.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/NavMenu/FluentNavMenu.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5345"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"u3HANg4jObqKg1Jso4ovjOp2lKuYeAN0+zlRIfKuHhw=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u3HANg4jObqKg1Jso4ovjOp2lKuYeAN0+zlRIfKuHhw="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/NavMenu/FluentNavMenu.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/NavMenu/FluentNavMenu.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000983284169"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1016"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"V831rKT4rCpErJth1Z/6nQ2pjce3CEI7KGHIdbWXhx8=\""}, {"Name": "ETag", "Value": "W/\"u3HANg4jObqKg1Jso4ovjOp2lKuYeAN0+zlRIfKuHhw=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u3HANg4jObqKg1Jso4ovjOp2lKuYeAN0+zlRIfKuHhw="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/NavMenu/FluentNavMenu.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/NavMenu/FluentNavMenu.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1016"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"V831rKT4rCpErJth1Z/6nQ2pjce3CEI7KGHIdbWXhx8=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V831rKT4rCpErJth1Z/6nQ2pjce3CEI7KGHIdbWXhx8="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overflow/FluentOverflow.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overflow/FluentOverflow.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6575"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hVi+eZ1AhYzWA2HILBTSjl5xstub4DMGzUxGJIQgjVo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hVi+eZ1AhYzWA2HILBTSjl5xstub4DMGzUxGJIQgjVo="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overflow/FluentOverflow.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overflow/FluentOverflow.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000572082380"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1747"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"fMg9R056t4+Y6PyTy3Auu6F5M2p0mm1ICT5RlLGEPQw=\""}, {"Name": "ETag", "Value": "W/\"hVi+eZ1AhYzWA2HILBTSjl5xstub4DMGzUxGJIQgjVo=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hVi+eZ1AhYzWA2HILBTSjl5xstub4DMGzUxGJIQgjVo="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overflow/FluentOverflow.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overflow/FluentOverflow.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1747"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"fMg9R056t4+Y6PyTy3Auu6F5M2p0mm1ICT5RlLGEPQw=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fMg9R056t4+Y6PyTy3Auu6F5M2p0mm1ICT5RlLGEPQw="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overlay/FluentOverlay.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overlay/FluentOverlay.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1977"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"IDySDi264SKaXFu1nL+hU2NeFhEMrX6Zv7ubUPR88VI=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IDySDi264SKaXFu1nL+hU2NeFhEMrX6Zv7ubUPR88VI="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overlay/FluentOverlay.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overlay/FluentOverlay.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001531393568"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "652"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Si6lAYTROr2Y2E+yQLNmoIuyol3tTTH2x0JT3bD9qzM=\""}, {"Name": "ETag", "Value": "W/\"IDySDi264SKaXFu1nL+hU2NeFhEMrX6Zv7ubUPR88VI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IDySDi264SKaXFu1nL+hU2NeFhEMrX6Zv7ubUPR88VI="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overlay/FluentOverlay.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overlay/FluentOverlay.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "652"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Si6lAYTROr2Y2E+yQLNmoIuyol3tTTH2x0JT3bD9qzM=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Si6lAYTROr2Y2E+yQLNmoIuyol3tTTH2x0JT3bD9qzM="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/PullToRefresh/FluentPullToRefresh.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/PullToRefresh/FluentPullToRefresh.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6841"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xlA5fSAkA6TiFUznwHP835N8kAxJ7YJ5MTizYCGeOfo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xlA5fSAkA6TiFUznwHP835N8kAxJ7YJ5MTizYCGeOfo="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/PullToRefresh/FluentPullToRefresh.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/PullToRefresh/FluentPullToRefresh.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000544365814"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1836"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gR+Q2B3kReBjCAmxaX0jrZ+LKn/mCFQH9svQins3Ffg=\""}, {"Name": "ETag", "Value": "W/\"xlA5fSAkA6TiFUznwHP835N8kAxJ7YJ5MTizYCGeOfo=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xlA5fSAkA6TiFUznwHP835N8kAxJ7YJ5MTizYCGeOfo="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/PullToRefresh/FluentPullToRefresh.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/PullToRefresh/FluentPullToRefresh.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1836"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gR+Q2B3kReBjCAmxaX0jrZ+LKn/mCFQH9svQins3Ffg=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gR+Q2B3kReBjCAmxaX0jrZ+LKn/mCFQH9svQins3Ffg="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Search/FluentSearch.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Search/FluentSearch.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "917"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"FpN8ZcuZyVhdYb+cHNB4VZ5bLM+yi3gDaTZbWsahaYE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FpN8ZcuZyVhdYb+cHNB4VZ5bLM+yi3gDaTZbWsahaYE="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Search/FluentSearch.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Search/FluentSearch.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002985074627"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "334"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eW7o1Dje91+P1j/ydSB4TyhEVA3pV/cMFp5ETKNpf94=\""}, {"Name": "ETag", "Value": "W/\"FpN8ZcuZyVhdYb+cHNB4VZ5bLM+yi3gDaTZbWsahaYE=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FpN8ZcuZyVhdYb+cHNB4VZ5bLM+yi3gDaTZbWsahaYE="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Search/FluentSearch.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Search/FluentSearch.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "334"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eW7o1Dje91+P1j/ydSB4TyhEVA3pV/cMFp5ETKNpf94=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eW7o1Dje91+P1j/ydSB4TyhEVA3pV/cMFp5ETKNpf94="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSlider.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSlider.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "445"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"TAnVg0aJviMtvE8pWYaaZahF5suJcjonGCC7accq76k=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TAnVg0aJviMtvE8pWYaaZahF5suJcjonGCC7accq76k="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSlider.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSlider.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003496503497"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "285"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"G3dG1kRqAVy5qnnnPHCI1eiXWJKlPgYyT43usniQ3po=\""}, {"Name": "ETag", "Value": "W/\"TAnVg0aJviMtvE8pWYaaZahF5suJcjonGCC7accq76k=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TAnVg0aJviMtvE8pWYaaZahF5suJcjonGCC7accq76k="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSlider.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSlider.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "285"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"G3dG1kRqAVy5qnnnPHCI1eiXWJKlPgYyT43usniQ3po=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-G3dG1kRqAVy5qnnnPHCI1eiXWJKlPgYyT43usniQ3po="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSliderLabel.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSliderLabel.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "340"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Em8bsrj69skLLR4IHVJ8lIJTR1EcY/U9nvcfn9t1rzo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Em8bsrj69skLLR4IHVJ8lIJTR1EcY/U9nvcfn9t1rzo="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSliderLabel.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSliderLabel.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.004464285714"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "223"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ge8qfaevzXuPADoLXJRU/hwb7Kf7ZbPVfpfiy5/WzSE=\""}, {"Name": "ETag", "Value": "W/\"Em8bsrj69skLLR4IHVJ8lIJTR1EcY/U9nvcfn9t1rzo=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Em8bsrj69skLLR4IHVJ8lIJTR1EcY/U9nvcfn9t1rzo="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSliderLabel.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSliderLabel.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "223"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ge8qfaevzXuPADoLXJRU/hwb7Kf7ZbPVfpfiy5/WzSE=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ge8qfaevzXuPADoLXJRU/hwb7Kf7ZbPVfpfiy5/WzSE="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/SortableList/FluentSortableList.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/SortableList/FluentSortableList.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1325"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rBxLYd0QGHwfD9IZljh74Lf+ZC+zqoRLqwikRKcRgpg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rBxLYd0QGHwfD9IZljh74Lf+ZC+zqoRLqwikRKcRgpg="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/SortableList/FluentSortableList.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/SortableList/FluentSortableList.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002114164905"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "472"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KrpRzB4c//N15dQfcbeepiBFyskX0d9lId+TSbEmr0g=\""}, {"Name": "ETag", "Value": "W/\"rBxLYd0QGHwfD9IZljh74Lf+ZC+zqoRLqwikRKcRgpg=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rBxLYd0QGHwfD9IZljh74Lf+ZC+zqoRLqwikRKcRgpg="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/SortableList/FluentSortableList.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/SortableList/FluentSortableList.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "472"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KrpRzB4c//N15dQfcbeepiBFyskX0d9lId+TSbEmr0g=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KrpRzB4c//N15dQfcbeepiBFyskX0d9lId+TSbEmr0g="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Splitter/FluentMultiSplitter.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Splitter/FluentMultiSplitter.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6140"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kExJSsKpmByqtTJ/TOwptCU5yawR+13aqkZxoVN+a1A=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kExJSsKpmByqtTJ/TOwptCU5yawR+13aqkZxoVN+a1A="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Splitter/FluentMultiSplitter.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Splitter/FluentMultiSplitter.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000745712155"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1340"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"IrDP3f1jd+L0Llm1IWUdLxzfiqNeIJ3gein+ePzMnDw=\""}, {"Name": "ETag", "Value": "W/\"kExJSsKpmByqtTJ/TOwptCU5yawR+13aqkZxoVN+a1A=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kExJSsKpmByqtTJ/TOwptCU5yawR+13aqkZxoVN+a1A="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Splitter/FluentMultiSplitter.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Splitter/FluentMultiSplitter.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1340"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"IrDP3f1jd+L0Llm1IWUdLxzfiqNeIJ3gein+ePzMnDw=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IrDP3f1jd+L0Llm1IWUdLxzfiqNeIJ3gein+ePzMnDw="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tabs/FluentTab.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tabs/FluentTab.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "526"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Kh0YI9vhH0m+YJJvQVdOvtm0zuIIGEdRv3aH6iv7Gcg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Kh0YI9vhH0m+YJJvQVdOvtm0zuIIGEdRv3aH6iv7Gcg="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tabs/FluentTab.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tabs/FluentTab.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003412969283"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "292"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"TR1S8EwhHXGT0IDFQjqJEelFP9eV7MQuyLQoSh0H3hM=\""}, {"Name": "ETag", "Value": "W/\"Kh0YI9vhH0m+YJJvQVdOvtm0zuIIGEdRv3aH6iv7Gcg=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Kh0YI9vhH0m+YJJvQVdOvtm0zuIIGEdRv3aH6iv7Gcg="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tabs/FluentTab.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tabs/FluentTab.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "292"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"TR1S8EwhHXGT0IDFQjqJEelFP9eV7MQuyLQoSh0H3hM=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TR1S8EwhHXGT0IDFQjqJEelFP9eV7MQuyLQoSh0H3hM="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/TextField/FluentTextField.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/TextField/FluentTextField.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1187"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YXiMRc9QPIiDSy+mlSF6DtYiSYb3X+1xlsCmrMrE2IU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YXiMRc9QPIiDSy+mlSF6DtYiSYb3X+1xlsCmrMrE2IU="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/TextField/FluentTextField.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/TextField/FluentTextField.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002150537634"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "464"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"m+bgmT5d1joH0Fxa7CS+eJ+VwQ3mfXBcVitL9BGnJDA=\""}, {"Name": "ETag", "Value": "W/\"YXiMRc9QPIiDSy+mlSF6DtYiSYb3X+1xlsCmrMrE2IU=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YXiMRc9QPIiDSy+mlSF6DtYiSYb3X+1xlsCmrMrE2IU="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/TextField/FluentTextField.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/TextField/FluentTextField.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "464"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"m+bgmT5d1joH0Fxa7CS+eJ+VwQ3mfXBcVitL9BGnJDA=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m+bgmT5d1joH0Fxa7CS+eJ+VwQ3mfXBcVitL9BGnJDA="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Toolbar/FluentToolbar.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Toolbar/FluentToolbar.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1364"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"s2w5uif33eV2OeQRoRzZYM1ANZXb6He68mkQ3IZw9Bc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-s2w5uif33eV2OeQRoRzZYM1ANZXb6He68mkQ3IZw9Bc="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Toolbar/FluentToolbar.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Toolbar/FluentToolbar.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001953125000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "511"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tOg3Q58NSmbnSFU3YAHG8lAku8SwpggUpqTf+AnATaA=\""}, {"Name": "ETag", "Value": "W/\"s2w5uif33eV2OeQRoRzZYM1ANZXb6He68mkQ3IZw9Bc=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-s2w5uif33eV2OeQRoRzZYM1ANZXb6He68mkQ3IZw9Bc="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Toolbar/FluentToolbar.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Toolbar/FluentToolbar.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "511"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tOg3Q58NSmbnSFU3YAHG8lAku8SwpggUpqTf+AnATaA=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tOg3Q58NSmbnSFU3YAHG8lAku8SwpggUpqTf+AnATaA="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tooltip/FluentTooltip.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tooltip/FluentTooltip.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "730"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pWY0aUTl5SagZBQwX/+DOHxke3fHSPoZdTQXbRQSFTU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pWY0aUTl5SagZBQwX/+DOHxke3fHSPoZdTQXbRQSFTU="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tooltip/FluentTooltip.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tooltip/FluentTooltip.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003571428571"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "279"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qYgukRZJexj+3utKj3bgZeh/3x4zsF0K/IgLGNtQZlU=\""}, {"Name": "ETag", "Value": "W/\"pWY0aUTl5SagZBQwX/+DOHxke3fHSPoZdTQXbRQSFTU=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pWY0aUTl5SagZBQwX/+DOHxke3fHSPoZdTQXbRQSFTU="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tooltip/FluentTooltip.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tooltip/FluentTooltip.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "279"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qYgukRZJexj+3utKj3bgZeh/3x4zsF0K/IgLGNtQZlU=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qYgukRZJexj+3utKj3bgZeh/3x4zsF0K/IgLGNtQZlU="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.bundle.scp.css", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "101053"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"zuaQLy76kNrlxjFX+6hX984arQH48y6Q/ghVBSpwWT0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 13:05:28 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zuaQLy76kNrlxjFX+6hX984arQH48y6Q/ghVBSpwWT0="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.bundle.scp.css", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000072311809"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13828"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ilM3cnZ9w1Cds3H/szasZoTjgcOxMI6YHlcwRSDZ87w=\""}, {"Name": "ETag", "Value": "W/\"zuaQLy76kNrlxjFX+6hX984arQH48y6Q/ghVBSpwWT0=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zuaQLy76kNrlxjFX+6hX984arQH48y6Q/ghVBSpwWT0="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "392863"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"htNuF2gbem5vP85zcYVih92CTJrIeZj4ghrN/AHd9VM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 13:05:19 GMT"}], "EndpointProperties": [{"Name": "dependency-group", "Value": "js-initializer"}, {"Name": "integrity", "Value": "sha256-htNuF2gbem5vP85zcYVih92CTJrIeZj4ghrN/AHd9VM="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010999406"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "90913"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ho7R4iSGK4SldCFlK5wThNxgWCzXFykru4YyKh0WJ5Y=\""}, {"Name": "ETag", "Value": "W/\"htNuF2gbem5vP85zcYVih92CTJrIeZj4ghrN/AHd9VM=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "dependency-group", "Value": "js-initializer"}, {"Name": "integrity", "Value": "sha256-htNuF2gbem5vP85zcYVih92CTJrIeZj4ghrN/AHd9VM="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1022"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gD29yOMICDIiYM16Dl8m2EwS2lyds8DoFkgTy29qko4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 13:05:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gD29yOMICDIiYM16Dl8m2EwS2lyds8DoFkgTy29qko4="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001736111111"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "575"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"2YmtOo2Bk8vpEVJoJ5uKDm6c1zUB6j043XC0D2H1AD0=\""}, {"Name": "ETag", "Value": "W/\"gD29yOMICDIiYM16Dl8m2EwS2lyds8DoFkgTy29qko4=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gD29yOMICDIiYM16Dl8m2EwS2lyds8DoFkgTy29qko4="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "575"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"2YmtOo2Bk8vpEVJoJ5uKDm6c1zUB6j043XC0D2H1AD0=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2YmtOo2Bk8vpEVJoJ5uKDm6c1zUB6j043XC0D2H1AD0="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "90913"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ho7R4iSGK4SldCFlK5wThNxgWCzXFykru4YyKh0WJ5Y=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ho7R4iSGK4SldCFlK5wThNxgWCzXFykru4YyKh0WJ5Y="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1313081"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"VHCb/U7GL6QVbx5psaGUdfmCzhTRPWRUuIbJLkEigYo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 13:05:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VHCb/U7GL6QVbx5psaGUdfmCzhTRPWRUuIbJLkEigYo="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000003594498"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "278202"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"oLgcOgt815aO1mw0Btae91K0hEcxrB2dJEZkTGZ996A=\""}, {"Name": "ETag", "Value": "W/\"VHCb/U7GL6QVbx5psaGUdfmCzhTRPWRUuIbJLkEigYo=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VHCb/U7GL6QVbx5psaGUdfmCzhTRPWRUuIbJLkEigYo="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "278202"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"oLgcOgt815aO1mw0Btae91K0hEcxrB2dJEZkTGZ996A=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oLgcOgt815aO1mw0Btae91K0hEcxrB2dJEZkTGZ996A="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "101053"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"zuaQLy76kNrlxjFX+6hX984arQH48y6Q/ghVBSpwWT0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 13:05:28 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q21vm7bk8w"}, {"Name": "integrity", "Value": "sha256-zuaQLy76kNrlxjFX+6hX984arQH48y6Q/ghVBSpwWT0="}, {"Name": "label", "Value": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.bundle.scp.css"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13828"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ilM3cnZ9w1Cds3H/szasZoTjgcOxMI6YHlcwRSDZ87w=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ilM3cnZ9w1Cds3H/szasZoTjgcOxMI6YHlcwRSDZ87w="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.1dlotxxwer.css", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7992"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2wyFQ9++b6uYwv3gv265xtRV2OWnPQMN68NpUHffScU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1dlotxxwer"}, {"Name": "integrity", "Value": "sha256-2wyFQ9++b6uYwv3gv265xtRV2OWnPQMN68NpUHffScU="}, {"Name": "label", "Value": "_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.css"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.css", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7992"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2wyFQ9++b6uYwv3gv265xtRV2OWnPQMN68NpUHffScU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2wyFQ9++b6uYwv3gv265xtRV2OWnPQMN68NpUHffScU="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.css", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000458295142"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2181"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"yWJK/jJRjYMvTfcO31YHBmrdz81VC2EyYHDDmtydagw=\""}, {"Name": "ETag", "Value": "W/\"2wyFQ9++b6uYwv3gv265xtRV2OWnPQMN68NpUHffScU=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2wyFQ9++b6uYwv3gv265xtRV2OWnPQMN68NpUHffScU="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.css.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2181"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"yWJK/jJRjYMvTfcO31YHBmrdz81VC2EyYHDDmtydagw=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yWJK/jJRjYMvTfcO31YHBmrdz81VC2EyYHDDmtydagw="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/initializersLoader.webview.f8c5bd5212.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/js/initializersLoader.webview.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1121"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"L9w4Nw5htE5XBWcy0I11eRfWwkTxtN8VSJWnitKu30Q=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "f8c5bd5212"}, {"Name": "integrity", "Value": "sha256-L9w4Nw5htE5XBWcy0I11eRfWwkTxtN8VSJWnitKu30Q="}, {"Name": "label", "Value": "_content/Microsoft.FluentUI.AspNetCore.Components/js/initializersLoader.webview.js"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/initializersLoader.webview.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/js/initializersLoader.webview.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1121"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"L9w4Nw5htE5XBWcy0I11eRfWwkTxtN8VSJWnitKu30Q=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L9w4Nw5htE5XBWcy0I11eRfWwkTxtN8VSJWnitKu30Q="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/initializersLoader.webview.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/js/initializersLoader.webview.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001941747573"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "514"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"whbSv+mD/ml9KCw7jXjGBPg1LWnmjRIlmI9T2b4UiTE=\""}, {"Name": "ETag", "Value": "W/\"L9w4Nw5htE5XBWcy0I11eRfWwkTxtN8VSJWnitKu30Q=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L9w4Nw5htE5XBWcy0I11eRfWwkTxtN8VSJWnitKu30Q="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/initializersLoader.webview.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/js/initializersLoader.webview.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "514"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"whbSv+mD/ml9KCw7jXjGBPg1LWnmjRIlmI9T2b4UiTE=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-whbSv+mD/ml9KCw7jXjGBPg1LWnmjRIlmI9T2b4UiTE="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3190"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kX+9ky61TMxar94Z7+S8myontpvgH4571DVehjxVvM4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kX+9ky61TMxar94Z7+S8myontpvgH4571DVehjxVvM4="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000841042893"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1188"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"P92YOWDKjxE0y0TlxQ45fEOnMJLoTqI3ePaSkqMS2GE=\""}, {"Name": "ETag", "Value": "W/\"kX+9ky61TMxar94Z7+S8myontpvgH4571DVehjxVvM4=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kX+9ky61TMxar94Z7+S8myontpvgH4571DVehjxVvM4="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1188"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"P92YOWDKjxE0y0TlxQ45fEOnMJLoTqI3ePaSkqMS2GE=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-P92YOWDKjxE0y0TlxQ45fEOnMJLoTqI3ePaSkqMS2GE="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.t5s4sbrbsi.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3190"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kX+9ky61TMxar94Z7+S8myontpvgH4571DVehjxVvM4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t5s4sbrbsi"}, {"Name": "integrity", "Value": "sha256-kX+9ky61TMxar94Z7+S8myontpvgH4571DVehjxVvM4="}, {"Name": "label", "Value": "_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.js"}]}, {"Route": "app.css", "AssetFile": "app.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000419991600"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2380"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DVrr9NspEdIaqVmzWqlS/YCtNIAp20RehYwFf11WU9k=\""}, {"Name": "ETag", "Value": "W/\"DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A="}]}, {"Route": "app.css", "AssetFile": "app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5583"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:34:33 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A="}]}, {"Route": "app.css.gz", "AssetFile": "app.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2380"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DVrr9NspEdIaqVmzWqlS/YCtNIAp20RehYwFf11WU9k=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DVrr9NspEdIaqVmzWqlS/YCtNIAp20RehYwFf11WU9k="}]}, {"Route": "app.kwazt7t2v0.css", "AssetFile": "app.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000419991600"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2380"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DVrr9NspEdIaqVmzWqlS/YCtNIAp20RehYwFf11WU9k=\""}, {"Name": "ETag", "Value": "W/\"DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kwazt7t2v0"}, {"Name": "integrity", "Value": "sha256-DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A="}, {"Name": "label", "Value": "app.css"}]}, {"Route": "app.kwazt7t2v0.css", "AssetFile": "app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5583"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:34:33 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kwazt7t2v0"}, {"Name": "integrity", "Value": "sha256-DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A="}, {"Name": "label", "Value": "app.css"}]}, {"Route": "app.kwazt7t2v0.css.gz", "AssetFile": "app.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2380"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DVrr9NspEdIaqVmzWqlS/YCtNIAp20RehYwFf11WU9k=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kwazt7t2v0"}, {"Name": "integrity", "Value": "sha256-DVrr9NspEdIaqVmzWqlS/YCtNIAp20RehYwFf11WU9k="}, {"Name": "label", "Value": "app.css.gz"}]}, {"Route": "favicon.a8m5cweeeb.ico", "AssetFile": "favicon.ico.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000182415177"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5481"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"P6HBbEJ0eZi/m1PmO8SAHdJL1XHEjrq2+bDgcIheHt0=\""}, {"Name": "ETag", "Value": "W/\"ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a8m5cweeeb"}, {"Name": "integrity", "Value": "sha256-ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs="}, {"Name": "label", "Value": "favicon.ico"}]}, {"Route": "favicon.a8m5cweeeb.ico", "AssetFile": "favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "15086"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:34:33 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a8m5cweeeb"}, {"Name": "integrity", "Value": "sha256-ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs="}, {"Name": "label", "Value": "favicon.ico"}]}, {"Route": "favicon.a8m5cweeeb.ico.gz", "AssetFile": "favicon.ico.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5481"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"P6HBbEJ0eZi/m1PmO8SAHdJL1XHEjrq2+bDgcIheHt0=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a8m5cweeeb"}, {"Name": "integrity", "Value": "sha256-P6HBbEJ0eZi/m1PmO8SAHdJL1XHEjrq2+bDgcIheHt0="}, {"Name": "label", "Value": "favicon.ico.gz"}]}, {"Route": "favicon.ico", "AssetFile": "favicon.ico.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000182415177"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5481"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"P6HBbEJ0eZi/m1PmO8SAHdJL1XHEjrq2+bDgcIheHt0=\""}, {"Name": "ETag", "Value": "W/\"ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs="}]}, {"Route": "favicon.ico", "AssetFile": "favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "15086"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:34:33 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs="}]}, {"Route": "favicon.ico.gz", "AssetFile": "favicon.ico.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5481"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"P6HBbEJ0eZi/m1PmO8SAHdJL1XHEjrq2+bDgcIheHt0=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-P6HBbEJ0eZi/m1PmO8SAHdJL1XHEjrq2+bDgcIheHt0="}]}]}