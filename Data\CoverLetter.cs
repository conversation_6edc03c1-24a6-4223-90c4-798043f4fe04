using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CareerPortal.Data
{
    public class CoverLetter
    {
        public int Id { get; set; }

        [Required]
        public int CandidateProfileId { get; set; }
        [ForeignKey("CandidateProfileId")]
        public CandidateProfile? CandidateProfile { get; set; }

        [Required]
        [StringLength(100)]
        public string? Title { get; set; }

        [Required]
        public string? Content { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        public DateTime LastModifiedDate { get; set; } = DateTime.UtcNow;
    }
}