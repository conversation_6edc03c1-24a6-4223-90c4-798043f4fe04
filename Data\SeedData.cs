using Microsoft.EntityFrameworkCore;

namespace CareerPortal.Data
{
    public static class SeedData
    {
        public static async Task SeedAsync(ApplicationDbContext context)
        {
            // Check if we already have data
            if (await context.Vacancies.AnyAsync())
            {
                return; // Database has been seeded
            }

            var vacancies = new List<Vacancy>
            {
                new Vacancy
                {
                    Title = "Software Engineer",
                    Description = "Join our IT team to develop innovative software solutions for automotive systems and business operations. Work with cutting-edge technologies and contribute to digital transformation initiatives.",
                    Responsibilities = "Develop and maintain software applications, collaborate with cross-functional teams, participate in code reviews, and ensure high-quality deliverables.",
                    Qualifications = "Bachelor's degree in Computer Science or related field, 2+ years of experience in software development, proficiency in C#, .NET, and SQL Server.",
                    Location = "Karachi",
                    Department = "Information Technology",
                    PostedDate = DateTime.UtcNow.AddDays(-2),
                    ClosingDate = DateTime.UtcNow.AddDays(28),
                    IsActive = true
                },
                new Vacancy
                {
                    Title = "Production Manager",
                    Description = "Lead our production team to ensure efficient manufacturing processes and quality standards. Drive operational excellence and continuous improvement initiatives.",
                    Responsibilities = "Oversee daily production operations, manage production schedules, ensure quality standards, lead team development, and implement process improvements.",
                    Qualifications = "Bachelor's degree in Engineering or related field, 5+ years of manufacturing experience, strong leadership skills, and knowledge of lean manufacturing principles.",
                    Location = "Lahore",
                    Department = "Manufacturing",
                    PostedDate = DateTime.UtcNow.AddDays(-5),
                    ClosingDate = DateTime.UtcNow.AddDays(25),
                    IsActive = true
                },
                new Vacancy
                {
                    Title = "Sales Executive",
                    Description = "Drive sales growth and build strong relationships with customers in the automotive market. Represent Pak Suzuki's commitment to excellence and customer satisfaction.",
                    Responsibilities = "Develop and maintain customer relationships, achieve sales targets, conduct product presentations, and provide excellent customer service.",
                    Qualifications = "Bachelor's degree in Business or related field, 2+ years of sales experience, excellent communication skills, and automotive industry knowledge preferred.",
                    Location = "Islamabad",
                    Department = "Sales",
                    PostedDate = DateTime.UtcNow.AddDays(-8),
                    ClosingDate = DateTime.UtcNow.AddDays(22),
                    IsActive = true
                },
                new Vacancy
                {
                    Title = "Quality Assurance Engineer",
                    Description = "Ensure the highest quality standards in our automotive manufacturing processes. Implement quality control measures and drive continuous improvement.",
                    Responsibilities = "Develop quality control procedures, conduct inspections, analyze quality data, and collaborate with production teams to resolve quality issues.",
                    Qualifications = "Bachelor's degree in Engineering, 3+ years of QA experience, knowledge of quality management systems, and automotive industry experience preferred.",
                    Location = "Karachi",
                    Department = "Quality Assurance",
                    PostedDate = DateTime.UtcNow.AddDays(-10),
                    ClosingDate = DateTime.UtcNow.AddDays(20),
                    IsActive = true
                },
                new Vacancy
                {
                    Title = "Human Resources Manager",
                    Description = "Lead HR initiatives to attract, develop, and retain top talent. Drive organizational development and employee engagement programs.",
                    Responsibilities = "Manage recruitment processes, develop HR policies, conduct performance management, and lead employee development initiatives.",
                    Qualifications = "Bachelor's degree in HR or related field, 5+ years of HR experience, strong interpersonal skills, and knowledge of employment law.",
                    Location = "Karachi",
                    Department = "Human Resources",
                    PostedDate = DateTime.UtcNow.AddDays(-12),
                    ClosingDate = DateTime.UtcNow.AddDays(18),
                    IsActive = true
                },
                new Vacancy
                {
                    Title = "Marketing Specialist",
                    Description = "Develop and execute marketing strategies to promote Pak Suzuki vehicles and services. Create compelling campaigns that resonate with our target audience.",
                    Responsibilities = "Plan marketing campaigns, manage social media presence, analyze market trends, and coordinate with advertising agencies.",
                    Qualifications = "Bachelor's degree in Marketing or related field, 2+ years of marketing experience, creativity, and digital marketing skills.",
                    Location = "Lahore",
                    Department = "Marketing",
                    PostedDate = DateTime.UtcNow.AddDays(-15),
                    ClosingDate = DateTime.UtcNow.AddDays(15),
                    IsActive = true
                }
            };

            context.Vacancies.AddRange(vacancies);
            await context.SaveChangesAsync();
        }
    }
}
