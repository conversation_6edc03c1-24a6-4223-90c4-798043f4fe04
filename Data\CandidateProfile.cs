using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CareerPortal.Data
{
    public class CandidateProfile
    {
        public int Id { get; set; }

        // Foreign key to ApplicationUser
        [Required]
        public string? ApplicationUserId { get; set; }
        public ApplicationUser? ApplicationUser { get; set; }

        [StringLength(100)]
        public string? FirstName { get; set; }

        [StringLength(100)]
        public string? LastName { get; set; }

        // To be encrypted
        [StringLength(20)]
        public string? MobileNumber { get; set; }

        // To be encrypted
        [StringLength(20)]
        public string? PhoneNumber { get; set; }

        // To be encrypted
        [StringLength(255)]
        public string? Address { get; set; }

        // To be encrypted
        [StringLength(20)]
        public string? CNIC { get; set; }

        // To be encrypted
        [StringLength(50)]
        public string? PassportNumber { get; set; }

        [StringLength(255)]
        public string? LinkedInProfileUrl { get; set; }

        // Store CV path or byte array depending on implementation
        public string? CvPath { get; set; } // Or byte[] CvContent

        public ICollection<Education>? Educations { get; set; }
        public ICollection<Experience>? Experiences { get; set; }
        public ICollection<TrainingCertification>? TrainingCertifications { get; set; }
        public ICollection<Skill>? Skills { get; set; }
        public ICollection<AchievementAward>? AchievementAwards { get; set; }
        public ICollection<CoverLetter>? CoverLetters { get; set; }
        public ICollection<JobAlertPreference>? JobAlertPreferences { get; set; }
        public ICollection<CandidateApplication>? Applications { get; set; }
    }
}