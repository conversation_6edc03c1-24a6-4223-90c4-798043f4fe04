using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CareerPortal.Data
{
    public class JobAlertPreference
    {
        public int Id { get; set; }

        [Required]
        public int CandidateProfileId { get; set; }
        [ForeignKey("CandidateProfileId")]
        public CandidateProfile? CandidateProfile { get; set; }

        [StringLength(100)]
        public string? Keywords { get; set; } // e.g., "Software Engineer, Karachi"

        [StringLength(50)]
        public string? LocationPreference { get; set; }

        [StringLength(50)]
        public string? JobTypePreference { get; set; } // e.g., Full-time, Part-time, Contract

        public bool IsActive { get; set; } = true;
    }
}