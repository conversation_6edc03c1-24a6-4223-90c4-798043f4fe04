{"Version": 1, "Hash": "6kQmnpgDwWA96pDAAPBStgs7uA+dpEKE+KGZXZGLbvw=", "Source": "CareerPort<PERSON>", "BasePath": "_content/CareerPortal", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "CareerPortal\\wwwroot", "Source": "CareerPort<PERSON>", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\", "BasePath": "_content/CareerPortal", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Anchor\\FluentAnchor.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Anchor/FluentAnchor.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "dpoev2zj9b", "Integrity": "b9RSPukLvSHekr3kftcukF9Hbr4g1a5l0/cfyJ61XMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Anchor\\FluentAnchor.razor.js", "FileLength": 430, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\AnchoredRegion\\FluentAnchoredRegion.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/AnchoredRegion/FluentAnchoredRegion.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "819w3ybe2d", "Integrity": "RC8P8iQcUu/cx7osATv0xSw2X8G3utkMtThwigsND5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\AnchoredRegion\\FluentAnchoredRegion.razor.js", "FileLength": 3215, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Button\\FluentButton.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Button/FluentButton.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "p6kf5zqzit", "Integrity": "8QTQtCTbbHkwqt3rAy8ZPjez2lZ6PGmR5Il+7Q3g/rs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Button\\FluentButton.razor.js", "FileLength": 522, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Checkbox\\FluentCheckbox.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Checkbox/FluentCheckbox.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "zjzit57lox", "Integrity": "gVrV4WI8finQdUGG7EIZIAh2tTbFW0GF7Hl73l/1JnE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Checkbox\\FluentCheckbox.razor.js", "FileLength": 368, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DataGrid\\FluentDataGrid.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/DataGrid/FluentDataGrid.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "nfhyg6xvey", "Integrity": "PbscDKK1vxqZeEv6a+4x9gKVhqxHVYgy9iE0fMp2iDY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DataGrid\\FluentDataGrid.razor.js", "FileLength": 14173, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DesignSystemProvider\\FluentDesignTheme.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/DesignSystemProvider/FluentDesignTheme.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vyjqmndgy2", "Integrity": "abaSp2xCB2qa0FHuc/9VKt39+MybEnIoOItPufNTSr4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DesignSystemProvider\\FluentDesignTheme.razor.js", "FileLength": 2058, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Divider\\FluentDivider.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Divider/FluentDivider.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "iy34mpf72d", "Integrity": "CndcCP/YVXs68LoE68COc38ypIJenMbJyu+fR0/ZIPc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Divider\\FluentDivider.razor.js", "FileLength": 388, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Grid\\FluentGrid.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Grid/FluentGrid.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hi1gwvth64", "Integrity": "V4iZz/kay7SoC/eRuDViVZkhxiL1oNW1gzMAFC6k/wY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Grid\\FluentGrid.razor.js", "FileLength": 3005, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\HorizontalScroll\\FluentHorizontalScroll.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/HorizontalScroll/FluentHorizontalScroll.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5pcucyxosc", "Integrity": "yf+15AR63QV4X8XvrAMxrEP5sX3Ea0tuh+Tsinb6yXU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\HorizontalScroll\\FluentHorizontalScroll.razor.js", "FileLength": 348, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\InputFile\\FluentInputFile.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/InputFile/FluentInputFile.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vjluklws0l", "Integrity": "m9D6O5smUPMQWbjax0bH03XYtdI3RD5geOwhizeT+gE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\InputFile\\FluentInputFile.razor.js", "FileLength": 2813, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\KeyCode\\FluentKeyCode.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/KeyCode/FluentKeyCode.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pu9hn1jugj", "Integrity": "3+jF/yOfwYyQhLujhQlSrvp3NBll+oEUF7v13pin53A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\KeyCode\\FluentKeyCode.razor.js", "FileLength": 3477, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Label\\FluentInputLabel.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Label/FluentInputLabel.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xp2f0e0rh3", "Integrity": "hXPNDHD1hTdz/sH1cD60f/ehIklf8zQAEE73UZNGtu8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Label\\FluentInputLabel.razor.js", "FileLength": 473, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentAutocomplete.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/List/FluentAutocomplete.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "psptt994gq", "Integrity": "2bhET+uXWbAao2aJyUqqscx9PObMTXmpUAkDQOQBGI8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentAutocomplete.razor.js", "FileLength": 1299, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentCombobox.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/List/FluentCombobox.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "afevzs963z", "Integrity": "OqLCO17dCq/aFFg8O0mXN/fF4czXAd6R+vgnYjtdPwc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentCombobox.razor.js", "FileLength": 1483, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\ListComponentBase.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/List/ListComponentBase.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "mmp1yy7un5", "Integrity": "/lFyXHGb/lh02BDFUuMzwbfU+zNOdnw2s2zKSrTtW00=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\ListComponentBase.razor.js", "FileLength": 177, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Menu\\FluentMenu.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Menu/FluentMenu.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5wrroj4j54", "Integrity": "C/YKywsVlWaSpZ1PLDeRKkkkM6ki2G2gT9ny+WVuERA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Menu\\FluentMenu.razor.js", "FileLength": 725, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\NavMenu\\FluentNavMenu.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/NavMenu/FluentNavMenu.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9fmja7pljs", "Integrity": "u3HANg4jObqKg1Jso4ovjOp2lKuYeAN0+zlRIfKuHhw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\NavMenu\\FluentNavMenu.razor.js", "FileLength": 5345, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overflow\\FluentOverflow.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Overflow/FluentOverflow.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "rgycuwl3sw", "Integrity": "hVi+eZ1AhYzWA2HILBTSjl5xstub4DMGzUxGJIQgjVo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overflow\\FluentOverflow.razor.js", "FileLength": 6575, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overlay\\FluentOverlay.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Overlay/FluentOverlay.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kjm33rwg1a", "Integrity": "IDySDi264SKaXFu1nL+hU2NeFhEMrX6Zv7ubUPR88VI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overlay\\FluentOverlay.razor.js", "FileLength": 1977, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\PullToRefresh\\FluentPullToRefresh.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/PullToRefresh/FluentPullToRefresh.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "awzanx0pu8", "Integrity": "xlA5fSAkA6TiFUznwHP835N8kAxJ7YJ5MTizYCGeOfo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\PullToRefresh\\FluentPullToRefresh.razor.js", "FileLength": 6841, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Search\\FluentSearch.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Search/FluentSearch.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "m0sdc2vg34", "Integrity": "FpN8ZcuZyVhdYb+cHNB4VZ5bLM+yi3gDaTZbWsahaYE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Search\\FluentSearch.razor.js", "FileLength": 917, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSlider.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Slider/FluentSlider.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "0b0bj86z40", "Integrity": "TAnVg0aJviMtvE8pWYaaZahF5suJcjonGCC7accq76k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSlider.razor.js", "FileLength": 445, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSliderLabel.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Slider/FluentSliderLabel.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "e5lgg05xwp", "Integrity": "Em8bsrj69skLLR4IHVJ8lIJTR1EcY/U9nvcfn9t1rzo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSliderLabel.razor.js", "FileLength": 340, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\SortableList\\FluentSortableList.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/SortableList/FluentSortableList.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ki10xp5gks", "Integrity": "rBxLYd0QGHwfD9IZljh74Lf+ZC+zqoRLqwikRKcRgpg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\SortableList\\FluentSortableList.razor.js", "FileLength": 1325, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Splitter\\FluentMultiSplitter.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Splitter/FluentMultiSplitter.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "s9hcthfn4x", "Integrity": "kExJSsKpmByqtTJ/TOwptCU5yawR+13aqkZxoVN+a1A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Splitter\\FluentMultiSplitter.razor.js", "FileLength": 6140, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tabs\\FluentTab.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Tabs/FluentTab.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "idf8r2y2gj", "Integrity": "Kh0YI9vhH0m+YJJvQVdOvtm0zuIIGEdRv3aH6iv7Gcg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tabs\\FluentTab.razor.js", "FileLength": 526, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\TextField\\FluentTextField.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/TextField/FluentTextField.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "btwuipzwbp", "Integrity": "YXiMRc9QPIiDSy+mlSF6DtYiSYb3X+1xlsCmrMrE2IU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\TextField\\FluentTextField.razor.js", "FileLength": 1187, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Toolbar\\FluentToolbar.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Toolbar/FluentToolbar.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "v95crb0bvb", "Integrity": "s2w5uif33eV2OeQRoRzZYM1ANZXb6He68mkQ3IZw9Bc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Toolbar\\FluentToolbar.razor.js", "FileLength": 1364, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tooltip\\FluentTooltip.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Tooltip/FluentTooltip.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "b0dyrub9as", "Integrity": "pWY0aUTl5SagZBQwX/+DOHxke3fHSPoZdTQXbRQSFTU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tooltip\\FluentTooltip.razor.js", "FileLength": 730, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\css\\reboot.css", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "css/reboot.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1dlotxxwer", "Integrity": "2wyFQ9++b6uYwv3gv265xtRV2OWnPQMN68NpUHffScU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\css\\reboot.css", "FileLength": 7992, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\initializersLoader.webview.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "js/initializersLoader.webview.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "f8c5bd5212", "Integrity": "L9w4Nw5htE5XBWcy0I11eRfWwkTxtN8VSJWnitKu30Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\initializersLoader.webview.js", "FileLength": 1121, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\loading-theme.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "js/loading-theme.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "t5s4sbrbsi", "Integrity": "kX+9ky61TMxar94Z7+S8myontpvgH4571DVehjxVvM4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\loading-theme.js", "FileLength": 3190, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "JSModule", "AssetTraitValue": "JSLibraryModule", "Fingerprint": "y92cxfqtgl", "Integrity": "htNuF2gbem5vP85zcYVih92CTJrIeZj4ghrN/AHd9VM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "FileLength": 392863, "LastWriteTime": "2025-02-18T13:05:18+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kz8gc8cxma", "Integrity": "gD29yOMICDIiYM16Dl8m2EwS2lyds8DoFkgTy29qko4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "FileLength": 1022, "LastWriteTime": "2025-02-18T13:05:18+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8j22j5h3b2", "Integrity": "VHCb/U7GL6QVbx5psaGUdfmCzhTRPWRUuIbJLkEigYo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "FileLength": 1313081, "LastWriteTime": "2025-02-18T13:05:18+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "q21vm7bk8w", "Integrity": "zuaQLy76kNrlxjFX+6hX984arQH48y6Q/ghVBSpwWT0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "FileLength": 101053, "LastWriteTime": "2025-02-18T13:05:28+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\2b4v2q74ar-0b0bj86z40.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Slider/FluentSlider.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSlider.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dix5wgjndd", "Integrity": "G3dG1kRqAVy5qnnnPHCI1eiXWJKlPgYyT43usniQ3po=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSlider.razor.js", "FileLength": 285, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\2hecizl59g-uhfllo7vmv.gz", "SourceId": "CareerPort<PERSON>", "SourceType": "Computed", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CareerPortal", "RelativePath": "CareerPortal.modules.json.gz", "AssetKind": "Build", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\jsmodules\\jsmodules.build.manifest.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wvxjj87u8r", "Integrity": "tMpc4Mp5Kart9mS3UELWfInMDPFZYORmj0wiAw7JwBs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\jsmodules\\jsmodules.build.manifest.json", "FileLength": 95, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\2qd731de3k-a8m5cweeeb.gz", "SourceId": "CareerPort<PERSON>", "SourceType": "Discovered", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CareerPortal", "RelativePath": "favicon#[.{fingerprint=a8m5cweeeb}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lv19d4ri1a", "Integrity": "P6HBbEJ0eZi/m1PmO8SAHdJL1XHEjrq2+bDgcIheHt0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\favicon.ico", "FileLength": 5481, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\3emkb5z5ma-8j22j5h3b2.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0sncgxxouj", "Integrity": "oLgcOgt815aO1mw0Btae91K0hEcxrB2dJEZkTGZ996A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "FileLength": 278202, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\4jvn6qpxj5-vyjqmndgy2.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/DesignSystemProvider/FluentDesignTheme.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DesignSystemProvider\\FluentDesignTheme.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fyjiiw4wfc", "Integrity": "ebidgEF0qxv0FVWWHIu9oAwtiTaKJTgfFFW7oNulicU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DesignSystemProvider\\FluentDesignTheme.razor.js", "FileLength": 751, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\4sjayj892b-9fmja7pljs.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/NavMenu/FluentNavMenu.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\NavMenu\\FluentNavMenu.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zhd8n844ok", "Integrity": "V831rKT4rCpErJth1Z/6nQ2pjce3CEI7KGHIdbWXhx8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\NavMenu\\FluentNavMenu.razor.js", "FileLength": 1016, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\5dcvuemm5a-s9hcthfn4x.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Splitter/FluentMultiSplitter.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Splitter\\FluentMultiSplitter.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6y9yx2mqnc", "Integrity": "IrDP3f1jd+L0Llm1IWUdLxzfiqNeIJ3gein+ePzMnDw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Splitter\\FluentMultiSplitter.razor.js", "FileLength": 1340, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\5dr5gsymsw-q21vm7bk8w.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mcb4qwo5eu", "Integrity": "ilM3cnZ9w1Cds3H/szasZoTjgcOxMI6YHlcwRSDZ87w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "FileLength": 13828, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\5eye7ha8pe-e5lgg05xwp.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Slider/FluentSliderLabel.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSliderLabel.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rdpoyavzo1", "Integrity": "ge8qfaevzXuPADoLXJRU/hwb7Kf7ZbPVfpfiy5/WzSE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSliderLabel.razor.js", "FileLength": 223, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\6mwvvlkmgj-hi1gwvth64.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Grid/FluentGrid.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Grid\\FluentGrid.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jk2pvfrq94", "Integrity": "QySeeEbzUPczOjYjev2fIc86i78Pib7zlTPyfCkLv+8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Grid\\FluentGrid.razor.js", "FileLength": 756, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\6nn42apl8y-1dlotxxwer.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "css/reboot.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\css\\reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lx5bc53mj0", "Integrity": "yWJK/jJRjYMvTfcO31YHBmrdz81VC2EyYHDDmtydagw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\css\\reboot.css", "FileLength": 2181, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\6o9gthdpke-psptt994gq.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/List/FluentAutocomplete.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentAutocomplete.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2vkc6rmnnw", "Integrity": "YtlDojVlcrK2+2QBRP4LPueVRl8gCsL6Ez4wknxUJLQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentAutocomplete.razor.js", "FileLength": 571, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\713i7dud0v-v95crb0bvb.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Toolbar/FluentToolbar.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Toolbar\\FluentToolbar.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gzh5leg0or", "Integrity": "tOg3Q58NSmbnSFU3YAHG8lAku8SwpggUpqTf+AnATaA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Toolbar\\FluentToolbar.razor.js", "FileLength": 511, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\7g337ulk4n-iy34mpf72d.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Divider/FluentDivider.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Divider\\FluentDivider.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1z47admwto", "Integrity": "E+0P+4Q5g9SzSGdE20j3R0nckgPdNoGc0x+TGJ/TIYI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Divider\\FluentDivider.razor.js", "FileLength": 233, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\8nrps8m2up-vzu5x4vng6.gz", "SourceId": "CareerPort<PERSON>", "SourceType": "Discovered", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CareerPortal", "RelativePath": "css/landing#[.{fingerprint=vzu5x4vng6}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\css\\landing.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "asj2wc7qd1", "Integrity": "Vm99OlISLLHxLmrZsC9T6bFROIQ+p9Ppt9xEqOgt0d8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\css\\landing.css", "FileLength": 1982, "LastWriteTime": "2025-05-31T09:36:49+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\9w4jwsdqh9-tnv30r1bl8.gz", "SourceId": "CareerPort<PERSON>", "SourceType": "Computed", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CareerPortal", "RelativePath": "CareerPortal#[.{fingerprint=tnv30r1bl8}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\scopedcss\\bundle\\CareerPortal.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "utdp7ws6s3", "Integrity": "Qjl948eSnEILonn3wELtC6BV9k8NVi6yotYo3mJkPSk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\scopedcss\\bundle\\CareerPortal.styles.css", "FileLength": 112, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\akzl5bxou0-p6kf5zqzit.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Button/FluentButton.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Button\\FluentButton.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h1hghvnkss", "Integrity": "RbdcZZJvJ98rtWAD8Wl13T6Jd/A0WUJIyozbvbTNME8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Button\\FluentButton.razor.js", "FileLength": 315, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\cdrmjjq1d6-dpoev2zj9b.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Anchor/FluentAnchor.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Anchor\\FluentAnchor.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7c3acd42hn", "Integrity": "mTB1BoNq7t/us6gvhhRKI480CaK7vT9E+t+AL9an8wU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Anchor\\FluentAnchor.razor.js", "FileLength": 267, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\cq4bmj8gmb-zjzit57lox.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Checkbox/FluentCheckbox.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Checkbox\\FluentCheckbox.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3qb8k9lguq", "Integrity": "pWpjnsF9tEf741/+XNZJIUp59j8Fqiqzv5qPR9T/F/k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Checkbox\\FluentCheckbox.razor.js", "FileLength": 224, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\ffdjjohsnc-mmp1yy7un5.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/List/ListComponentBase.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\ListComponentBase.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0g1tupjq4p", "Integrity": "kD64FhYDGF1SA7wVn4LL4nX1pTWXmxuMYqo2xXip44E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\ListComponentBase.razor.js", "FileLength": 137, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\fiqznfocyn-ki10xp5gks.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/SortableList/FluentSortableList.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\SortableList\\FluentSortableList.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mhffyjj8te", "Integrity": "KrpRzB4c//N15dQfcbeepiBFyskX0d9lId+TSbEmr0g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\SortableList\\FluentSortableList.razor.js", "FileLength": 472, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\h0qxm5u0yy-vjluklws0l.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/InputFile/FluentInputFile.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\InputFile\\FluentInputFile.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "je4eg9uejw", "Integrity": "y0GyYNSHHrdFmkoHrD7jQi0oehiE/vh7Pi7iAEYeorU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\InputFile\\FluentInputFile.razor.js", "FileLength": 833, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\hpxq4k1yf6-kwazt7t2v0.gz", "SourceId": "CareerPort<PERSON>", "SourceType": "Discovered", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CareerPortal", "RelativePath": "app#[.{fingerprint=kwazt7t2v0}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9tr1n8k5z8", "Integrity": "DVrr9NspEdIaqVmzWqlS/YCtNIAp20RehYwFf11WU9k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\app.css", "FileLength": 2380, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\ijuf810ucb-xp2f0e0rh3.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Label/FluentInputLabel.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Label\\FluentInputLabel.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5oe6m2x2uv", "Integrity": "xT1lGfZ95NQfS99lKqhohgyvrwO4H0pOZ0PZuIj2+Xo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Label\\FluentInputLabel.razor.js", "FileLength": 253, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\is0nail857-b0dyrub9as.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Tooltip/FluentTooltip.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tooltip\\FluentTooltip.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n43ij1gg6a", "Integrity": "qYgukRZJexj+3utKj3bgZeh/3x4zsF0K/IgLGNtQZlU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tooltip\\FluentTooltip.razor.js", "FileLength": 279, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\jq2jihf5fr-awzanx0pu8.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/PullToRefresh/FluentPullToRefresh.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\PullToRefresh\\FluentPullToRefresh.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5rv3cg16bo", "Integrity": "gR+Q2B3kReBjCAmxaX0jrZ+LKn/mCFQH9svQins3Ffg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\PullToRefresh\\FluentPullToRefresh.razor.js", "FileLength": 1836, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\klh9ieqob8-nfhyg6xvey.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/DataGrid/FluentDataGrid.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DataGrid\\FluentDataGrid.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1kw23c60ji", "Integrity": "UQRUr2KMtLUqt9ogdiIWMXFjGLFL3UBvDZCD4pGFuKQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DataGrid\\FluentDataGrid.razor.js", "FileLength": 2941, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\kwrall9b0s-y92cxfqtgl.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.lib.module.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mhsh70qfxk", "Integrity": "ho7R4iSGK4SldCFlK5wThNxgWCzXFykru4YyKh0WJ5Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "FileLength": 90913, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\m1yjzwotev-f8c5bd5212.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "js/initializersLoader.webview.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\initializersLoader.webview.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mz0b1j7sap", "Integrity": "whbSv+mD/ml9KCw7jXjGBPg1LWnmjRIlmI9T2b4UiTE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\initializersLoader.webview.js", "FileLength": 514, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\m3ffqup1h6-kz8gc8cxma.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3x8k8dzeqr", "Integrity": "2YmtOo2Bk8vpEVJoJ5uKDm6c1zUB6j043XC0D2H1AD0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "FileLength": 575, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\mm8wr5jb95-rgycuwl3sw.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Overflow/FluentOverflow.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overflow\\FluentOverflow.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4s5d9cwut2", "Integrity": "fMg9R056t4+Y6PyTy3Auu6F5M2p0mm1ICT5RlLGEPQw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overflow\\FluentOverflow.razor.js", "FileLength": 1747, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\pfc9to5k3q-5pcucyxosc.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/HorizontalScroll/FluentHorizontalScroll.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\HorizontalScroll\\FluentHorizontalScroll.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u6v528bpot", "Integrity": "2oCN2IEWRZPjloAzwScHmDLBlebfEgP366812p/E2c8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\HorizontalScroll\\FluentHorizontalScroll.razor.js", "FileLength": 141, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\pn46o4hdgm-5wrroj4j54.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Menu/FluentMenu.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Menu\\FluentMenu.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fn6hmwpiy", "Integrity": "8zrNWZqy7raoxnB2CdCgVMkZDFcG6ue/1K4DaARm2RI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Menu\\FluentMenu.razor.js", "FileLength": 298, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\ruk4ibgzyp-t5s4sbrbsi.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "js/loading-theme.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\loading-theme.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vs7zrwl0nd", "Integrity": "P92YOWDKjxE0y0TlxQ45fEOnMJLoTqI3ePaSkqMS2GE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\loading-theme.js", "FileLength": 1188, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\sbcuig79xm-btwuipzwbp.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/TextField/FluentTextField.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\TextField\\FluentTextField.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n5p97eiurb", "Integrity": "m+bgmT5d1joH0Fxa7CS+eJ+VwQ3mfXBcVitL9BGnJDA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\TextField\\FluentTextField.razor.js", "FileLength": 464, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\sc7iw8bo2n-idf8r2y2gj.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Tabs/FluentTab.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tabs\\FluentTab.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "razgc08jz5", "Integrity": "TR1S8EwhHXGT0IDFQjqJEelFP9eV7MQuyLQoSh0H3hM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tabs\\FluentTab.razor.js", "FileLength": 292, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\tb98bamshq-afevzs963z.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/List/FluentCombobox.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentCombobox.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f8na2y7sdz", "Integrity": "4fE5RW6001fSJc6+Ju/+RSpNSo5ZsvCuJri+aL4wof8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentCombobox.razor.js", "FileLength": 541, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\twd6d0wh4o-819w3ybe2d.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/AnchoredRegion/FluentAnchoredRegion.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\AnchoredRegion\\FluentAnchoredRegion.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0hkgbsy7ft", "Integrity": "pIZWamBcM95RyKOecGwL1QrEdg/IKc5rscB4MFCIujM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\AnchoredRegion\\FluentAnchoredRegion.razor.js", "FileLength": 996, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\uslingtyva-m0sdc2vg34.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Search/FluentSearch.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Search\\FluentSearch.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3dn9v1xa0n", "Integrity": "eW7o1Dje91+P1j/ydSB4TyhEVA3pV/cMFp5ETKNpf94=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Search\\FluentSearch.razor.js", "FileLength": 334, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\win2gt64sq-pu9hn1jugj.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/KeyCode/FluentKeyCode.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\KeyCode\\FluentKeyCode.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5y418xaf05", "Integrity": "TVs9bq3kylVYMAlXR3Kt4NTTdLquoTIUYrtItz80G30=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\KeyCode\\FluentKeyCode.razor.js", "FileLength": 888, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\wmgko3ir4p-kjm33rwg1a.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Overlay/FluentOverlay.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overlay\\FluentOverlay.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "azo1hjcky2", "Integrity": "Si6lAYTROr2Y2E+yQLNmoIuyol3tTTH2x0JT3bD9qzM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overlay\\FluentOverlay.razor.js", "FileLength": 652, "LastWriteTime": "2025-05-31T05:56:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\jsmodules\\jsmodules.build.manifest.json", "SourceId": "CareerPort<PERSON>", "SourceType": "Computed", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\jsmodules\\", "BasePath": "_content/CareerPortal", "RelativePath": "CareerPortal.modules.json", "AssetKind": "Build", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "JSModule", "AssetTraitValue": "JSModuleManifest", "Fingerprint": "uhfllo7vmv", "Integrity": "sgZOtYFoQbtT5NzEZj6mvNpqny3TPXduyD8nEg1wGig=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "obj\\Debug\\net9.0\\jsmodules\\jsmodules.build.manifest.json", "FileLength": 114, "LastWriteTime": "2025-05-31T05:55:26+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\scopedcss\\bundle\\CareerPortal.styles.css", "SourceId": "CareerPort<PERSON>", "SourceType": "Computed", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\scopedcss\\bundle\\", "BasePath": "_content/CareerPortal", "RelativePath": "CareerPortal#[.{fingerprint}]?.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "Fingerprint": "tnv30r1bl8", "Integrity": "Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\scopedcss\\bundle\\CareerPortal.styles.css", "FileLength": 131, "LastWriteTime": "2025-05-31T05:55:26+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\app.css", "SourceId": "CareerPort<PERSON>", "SourceType": "Discovered", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\", "BasePath": "_content/CareerPortal", "RelativePath": "app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kwazt7t2v0", "Integrity": "DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.css", "FileLength": 5583, "LastWriteTime": "2025-05-31T05:34:33+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\css\\landing.css", "SourceId": "CareerPort<PERSON>", "SourceType": "Discovered", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\", "BasePath": "_content/CareerPortal", "RelativePath": "css/landing#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vzu5x4vng6", "Integrity": "1dLgneFV9PDKZ94smr3NcKjcLGsRYSEpjirulYCmaPw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\landing.css", "FileLength": 7997, "LastWriteTime": "2025-05-31T09:36:09+00:00"}, {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\favicon.ico", "SourceId": "CareerPort<PERSON>", "SourceType": "Discovered", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\", "BasePath": "_content/CareerPortal", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "a8m5cweeeb", "Integrity": "ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 15086, "LastWriteTime": "2025-05-31T05:34:33+00:00"}], "Endpoints": [{"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Anchor/FluentAnchor.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Anchor\\FluentAnchor.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "430"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"b9RSPukLvSHekr3kftcukF9Hbr4g1a5l0/cfyJ61XMA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b9RSPukLvSHekr3kftcukF9Hbr4g1a5l0/cfyJ61XMA="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Anchor/FluentAnchor.razor.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\cdrmjjq1d6-dpoev2zj9b.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003731343284"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "267"}, {"Name": "ETag", "Value": "\"mTB1BoNq7t/us6gvhhRKI480CaK7vT9E+t+AL9an8wU=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"b9RSPukLvSHekr3kftcukF9Hbr4g1a5l0/cfyJ61XMA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b9RSPukLvSHekr3kftcukF9Hbr4g1a5l0/cfyJ61XMA="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Anchor/FluentAnchor.razor.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\cdrmjjq1d6-dpoev2zj9b.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "267"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mTB1BoNq7t/us6gvhhRKI480CaK7vT9E+t+AL9an8wU=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mTB1BoNq7t/us6gvhhRKI480CaK7vT9E+t+AL9an8wU="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/AnchoredRegion/FluentAnchoredRegion.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\AnchoredRegion\\FluentAnchoredRegion.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3215"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RC8P8iQcUu/cx7osATv0xSw2X8G3utkMtThwigsND5g=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RC8P8iQcUu/cx7osATv0xSw2X8G3utkMtThwigsND5g="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/AnchoredRegion/FluentAnchoredRegion.razor.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\twd6d0wh4o-819w3ybe2d.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001003009027"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "996"}, {"Name": "ETag", "Value": "\"pIZWamBcM95RyKOecGwL1QrEdg/IKc5rscB4MFCIujM=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"RC8P8iQcUu/cx7osATv0xSw2X8G3utkMtThwigsND5g=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RC8P8iQcUu/cx7osATv0xSw2X8G3utkMtThwigsND5g="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/AnchoredRegion/FluentAnchoredRegion.razor.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\twd6d0wh4o-819w3ybe2d.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "996"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pIZWamBcM95RyKOecGwL1QrEdg/IKc5rscB4MFCIujM=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pIZWamBcM95RyKOecGwL1QrEdg/IKc5rscB4MFCIujM="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Button/FluentButton.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Button\\FluentButton.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "522"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8QTQtCTbbHkwqt3rAy8ZPjez2lZ6PGmR5Il+7Q3g/rs=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8QTQtCTbbHkwqt3rAy8ZPjez2lZ6PGmR5Il+7Q3g/rs="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Button/FluentButton.razor.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\akzl5bxou0-p6kf5zqzit.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003164556962"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "315"}, {"Name": "ETag", "Value": "\"RbdcZZJvJ98rtWAD8Wl13T6Jd/A0WUJIyozbvbTNME8=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"8QTQtCTbbHkwqt3rAy8ZPjez2lZ6PGmR5Il+7Q3g/rs=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8QTQtCTbbHkwqt3rAy8ZPjez2lZ6PGmR5Il+7Q3g/rs="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Button/FluentButton.razor.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\akzl5bxou0-p6kf5zqzit.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "315"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RbdcZZJvJ98rtWAD8Wl13T6Jd/A0WUJIyozbvbTNME8=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RbdcZZJvJ98rtWAD8Wl13T6Jd/A0WUJIyozbvbTNME8="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Checkbox/FluentCheckbox.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Checkbox\\FluentCheckbox.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "368"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gVrV4WI8finQdUGG7EIZIAh2tTbFW0GF7Hl73l/1JnE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gVrV4WI8finQdUGG7EIZIAh2tTbFW0GF7Hl73l/1JnE="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Checkbox/FluentCheckbox.razor.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\cq4bmj8gmb-zjzit57lox.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.004444444444"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "224"}, {"Name": "ETag", "Value": "\"pWpjnsF9tEf741/+XNZJIUp59j8Fqiqzv5qPR9T/F/k=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"gVrV4WI8finQdUGG7EIZIAh2tTbFW0GF7Hl73l/1JnE=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gVrV4WI8finQdUGG7EIZIAh2tTbFW0GF7Hl73l/1JnE="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Checkbox/FluentCheckbox.razor.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\cq4bmj8gmb-zjzit57lox.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "224"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pWpjnsF9tEf741/+XNZJIUp59j8Fqiqzv5qPR9T/F/k=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pWpjnsF9tEf741/+XNZJIUp59j8Fqiqzv5qPR9T/F/k="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DataGrid/FluentDataGrid.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DataGrid\\FluentDataGrid.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14173"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"PbscDKK1vxqZeEv6a+4x9gKVhqxHVYgy9iE0fMp2iDY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PbscDKK1vxqZeEv6a+4x9gKVhqxHVYgy9iE0fMp2iDY="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DataGrid/FluentDataGrid.razor.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\klh9ieqob8-nfhyg6xvey.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000339904827"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2941"}, {"Name": "ETag", "Value": "\"UQRUr2KMtLUqt9ogdiIWMXFjGLFL3UBvDZCD4pGFuKQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"PbscDKK1vxqZeEv6a+4x9gKVhqxHVYgy9iE0fMp2iDY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PbscDKK1vxqZeEv6a+4x9gKVhqxHVYgy9iE0fMp2iDY="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DataGrid/FluentDataGrid.razor.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\klh9ieqob8-nfhyg6xvey.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2941"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UQRUr2KMtLUqt9ogdiIWMXFjGLFL3UBvDZCD4pGFuKQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UQRUr2KMtLUqt9ogdiIWMXFjGLFL3UBvDZCD4pGFuKQ="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DesignSystemProvider/FluentDesignTheme.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DesignSystemProvider\\FluentDesignTheme.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2058"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"abaSp2xCB2qa0FHuc/9VKt39+MybEnIoOItPufNTSr4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-abaSp2xCB2qa0FHuc/9VKt39+MybEnIoOItPufNTSr4="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DesignSystemProvider/FluentDesignTheme.razor.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\4jvn6qpxj5-vyjqmndgy2.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001329787234"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "751"}, {"Name": "ETag", "Value": "\"ebidgEF0qxv0FVWWHIu9oAwtiTaKJTgfFFW7oNulicU=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"abaSp2xCB2qa0FHuc/9VKt39+MybEnIoOItPufNTSr4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-abaSp2xCB2qa0FHuc/9VKt39+MybEnIoOItPufNTSr4="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DesignSystemProvider/FluentDesignTheme.razor.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\4jvn6qpxj5-vyjqmndgy2.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "751"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ebidgEF0qxv0FVWWHIu9oAwtiTaKJTgfFFW7oNulicU=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ebidgEF0qxv0FVWWHIu9oAwtiTaKJTgfFFW7oNulicU="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Divider/FluentDivider.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Divider\\FluentDivider.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "388"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CndcCP/YVXs68LoE68COc38ypIJenMbJyu+fR0/ZIPc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CndcCP/YVXs68LoE68COc38ypIJenMbJyu+fR0/ZIPc="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Divider/FluentDivider.razor.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\7g337ulk4n-iy34mpf72d.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.004273504274"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "233"}, {"Name": "ETag", "Value": "\"E+0P+4Q5g9SzSGdE20j3R0nckgPdNoGc0x+TGJ/TIYI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"CndcCP/YVXs68LoE68COc38ypIJenMbJyu+fR0/ZIPc=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CndcCP/YVXs68LoE68COc38ypIJenMbJyu+fR0/ZIPc="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Divider/FluentDivider.razor.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\7g337ulk4n-iy34mpf72d.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "233"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"E+0P+4Q5g9SzSGdE20j3R0nckgPdNoGc0x+TGJ/TIYI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-E+0P+4Q5g9SzSGdE20j3R0nckgPdNoGc0x+TGJ/TIYI="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Grid/FluentGrid.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Grid\\FluentGrid.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3005"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"V4iZz/kay7SoC/eRuDViVZkhxiL1oNW1gzMAFC6k/wY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V4iZz/kay7SoC/eRuDViVZkhxiL1oNW1gzMAFC6k/wY="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Grid/FluentGrid.razor.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\6mwvvlkmgj-hi1gwvth64.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001321003963"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "756"}, {"Name": "ETag", "Value": "\"QySeeEbzUPczOjYjev2fIc86i78Pib7zlTPyfCkLv+8=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"V4iZz/kay7SoC/eRuDViVZkhxiL1oNW1gzMAFC6k/wY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V4iZz/kay7SoC/eRuDViVZkhxiL1oNW1gzMAFC6k/wY="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Grid/FluentGrid.razor.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\6mwvvlkmgj-hi1gwvth64.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "756"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QySeeEbzUPczOjYjev2fIc86i78Pib7zlTPyfCkLv+8=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QySeeEbzUPczOjYjev2fIc86i78Pib7zlTPyfCkLv+8="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/HorizontalScroll/FluentHorizontalScroll.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\HorizontalScroll\\FluentHorizontalScroll.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "348"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yf+15AR63QV4X8XvrAMxrEP5sX3Ea0tuh+Tsinb6yXU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yf+15AR63QV4X8XvrAMxrEP5sX3Ea0tuh+Tsinb6yXU="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/HorizontalScroll/FluentHorizontalScroll.razor.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\pfc9to5k3q-5pcucyxosc.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.007042253521"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "141"}, {"Name": "ETag", "Value": "\"2oCN2IEWRZPjloAzwScHmDLBlebfEgP366812p/E2c8=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"yf+15AR63QV4X8XvrAMxrEP5sX3Ea0tuh+Tsinb6yXU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yf+15AR63QV4X8XvrAMxrEP5sX3Ea0tuh+Tsinb6yXU="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/HorizontalScroll/FluentHorizontalScroll.razor.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\pfc9to5k3q-5pcucyxosc.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "141"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"2oCN2IEWRZPjloAzwScHmDLBlebfEgP366812p/E2c8=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2oCN2IEWRZPjloAzwScHmDLBlebfEgP366812p/E2c8="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/InputFile/FluentInputFile.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\InputFile\\FluentInputFile.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2813"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"m9D6O5smUPMQWbjax0bH03XYtdI3RD5geOwhizeT+gE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m9D6O5smUPMQWbjax0bH03XYtdI3RD5geOwhizeT+gE="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/InputFile/FluentInputFile.razor.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\h0qxm5u0yy-vjluklws0l.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001199040767"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "833"}, {"Name": "ETag", "Value": "\"y0GyYNSHHrdFmkoHrD7jQi0oehiE/vh7Pi7iAEYeorU=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"m9D6O5smUPMQWbjax0bH03XYtdI3RD5geOwhizeT+gE=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m9D6O5smUPMQWbjax0bH03XYtdI3RD5geOwhizeT+gE="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/InputFile/FluentInputFile.razor.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\h0qxm5u0yy-vjluklws0l.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "833"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"y0GyYNSHHrdFmkoHrD7jQi0oehiE/vh7Pi7iAEYeorU=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-y0GyYNSHHrdFmkoHrD7jQi0oehiE/vh7Pi7iAEYeorU="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/KeyCode/FluentKeyCode.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\KeyCode\\FluentKeyCode.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3477"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3+jF/yOfwYyQhLujhQlSrvp3NBll+oEUF7v13pin53A=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3+jF/yOfwYyQhLujhQlSrvp3NBll+oEUF7v13pin53A="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/KeyCode/FluentKeyCode.razor.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\win2gt64sq-pu9hn1jugj.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001124859393"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "888"}, {"Name": "ETag", "Value": "\"TVs9bq3kylVYMAlXR3Kt4NTTdLquoTIUYrtItz80G30=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"3+jF/yOfwYyQhLujhQlSrvp3NBll+oEUF7v13pin53A=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3+jF/yOfwYyQhLujhQlSrvp3NBll+oEUF7v13pin53A="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/KeyCode/FluentKeyCode.razor.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\win2gt64sq-pu9hn1jugj.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "888"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"TVs9bq3kylVYMAlXR3Kt4NTTdLquoTIUYrtItz80G30=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TVs9bq3kylVYMAlXR3Kt4NTTdLquoTIUYrtItz80G30="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Label/FluentInputLabel.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Label\\FluentInputLabel.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "473"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hXPNDHD1hTdz/sH1cD60f/ehIklf8zQAEE73UZNGtu8=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hXPNDHD1hTdz/sH1cD60f/ehIklf8zQAEE73UZNGtu8="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Label/FluentInputLabel.razor.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\ijuf810ucb-xp2f0e0rh3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003937007874"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "253"}, {"Name": "ETag", "Value": "\"xT1lGfZ95NQfS99lKqhohgyvrwO4H0pOZ0PZuIj2+Xo=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"hXPNDHD1hTdz/sH1cD60f/ehIklf8zQAEE73UZNGtu8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hXPNDHD1hTdz/sH1cD60f/ehIklf8zQAEE73UZNGtu8="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Label/FluentInputLabel.razor.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\ijuf810ucb-xp2f0e0rh3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "253"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xT1lGfZ95NQfS99lKqhohgyvrwO4H0pOZ0PZuIj2+Xo=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xT1lGfZ95NQfS99lKqhohgyvrwO4H0pOZ0PZuIj2+Xo="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentAutocomplete.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentAutocomplete.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1299"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"2bhET+uXWbAao2aJyUqqscx9PObMTXmpUAkDQOQBGI8=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2bhET+uXWbAao2aJyUqqscx9PObMTXmpUAkDQOQBGI8="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentAutocomplete.razor.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\6o9gthdpke-psptt994gq.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001748251748"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "571"}, {"Name": "ETag", "Value": "\"YtlDojVlcrK2+2QBRP4LPueVRl8gCsL6Ez4wknxUJLQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"2bhET+uXWbAao2aJyUqqscx9PObMTXmpUAkDQOQBGI8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2bhET+uXWbAao2aJyUqqscx9PObMTXmpUAkDQOQBGI8="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentAutocomplete.razor.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\6o9gthdpke-psptt994gq.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "571"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YtlDojVlcrK2+2QBRP4LPueVRl8gCsL6Ez4wknxUJLQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YtlDojVlcrK2+2QBRP4LPueVRl8gCsL6Ez4wknxUJLQ="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentCombobox.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentCombobox.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1483"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OqLCO17dCq/aFFg8O0mXN/fF4czXAd6R+vgnYjtdPwc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OqLCO17dCq/aFFg8O0mXN/fF4czXAd6R+vgnYjtdPwc="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentCombobox.razor.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\tb98bamshq-afevzs963z.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001845018450"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "541"}, {"Name": "ETag", "Value": "\"4fE5RW6001fSJc6+Ju/+RSpNSo5ZsvCuJri+aL4wof8=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"OqLCO17dCq/aFFg8O0mXN/fF4czXAd6R+vgnYjtdPwc=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OqLCO17dCq/aFFg8O0mXN/fF4czXAd6R+vgnYjtdPwc="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentCombobox.razor.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\tb98bamshq-afevzs963z.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "541"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4fE5RW6001fSJc6+Ju/+RSpNSo5ZsvCuJri+aL4wof8=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4fE5RW6001fSJc6+Ju/+RSpNSo5ZsvCuJri+aL4wof8="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/ListComponentBase.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\ListComponentBase.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "177"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/lFyXHGb/lh02BDFUuMzwbfU+zNOdnw2s2zKSrTtW00=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/lFyXHGb/lh02BDFUuMzwbfU+zNOdnw2s2zKSrTtW00="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/ListComponentBase.razor.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\ffdjjohsnc-mmp1yy7un5.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.007246376812"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "137"}, {"Name": "ETag", "Value": "\"kD64FhYDGF1SA7wVn4LL4nX1pTWXmxuMYqo2xXip44E=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"/lFyXHGb/lh02BDFUuMzwbfU+zNOdnw2s2zKSrTtW00=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/lFyXHGb/lh02BDFUuMzwbfU+zNOdnw2s2zKSrTtW00="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/ListComponentBase.razor.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\ffdjjohsnc-mmp1yy7un5.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "137"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kD64FhYDGF1SA7wVn4LL4nX1pTWXmxuMYqo2xXip44E=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kD64FhYDGF1SA7wVn4LL4nX1pTWXmxuMYqo2xXip44E="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Menu/FluentMenu.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Menu\\FluentMenu.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "725"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"C/YKywsVlWaSpZ1PLDeRKkkkM6ki2G2gT9ny+WVuERA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-C/YKywsVlWaSpZ1PLDeRKkkkM6ki2G2gT9ny+WVuERA="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Menu/FluentMenu.razor.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\pn46o4hdgm-5wrroj4j54.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003344481605"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "298"}, {"Name": "ETag", "Value": "\"8zrNWZqy7raoxnB2CdCgVMkZDFcG6ue/1K4DaARm2RI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"C/YKywsVlWaSpZ1PLDeRKkkkM6ki2G2gT9ny+WVuERA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-C/YKywsVlWaSpZ1PLDeRKkkkM6ki2G2gT9ny+WVuERA="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Menu/FluentMenu.razor.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\pn46o4hdgm-5wrroj4j54.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "298"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8zrNWZqy7raoxnB2CdCgVMkZDFcG6ue/1K4DaARm2RI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8zrNWZqy7raoxnB2CdCgVMkZDFcG6ue/1K4DaARm2RI="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/NavMenu/FluentNavMenu.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\NavMenu\\FluentNavMenu.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5345"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"u3HANg4jObqKg1Jso4ovjOp2lKuYeAN0+zlRIfKuHhw=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u3HANg4jObqKg1Jso4ovjOp2lKuYeAN0+zlRIfKuHhw="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/NavMenu/FluentNavMenu.razor.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\4sjayj892b-9fmja7pljs.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000983284169"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1016"}, {"Name": "ETag", "Value": "\"V831rKT4rCpErJth1Z/6nQ2pjce3CEI7KGHIdbWXhx8=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"u3HANg4jObqKg1Jso4ovjOp2lKuYeAN0+zlRIfKuHhw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u3HANg4jObqKg1Jso4ovjOp2lKuYeAN0+zlRIfKuHhw="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/NavMenu/FluentNavMenu.razor.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\4sjayj892b-9fmja7pljs.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1016"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"V831rKT4rCpErJth1Z/6nQ2pjce3CEI7KGHIdbWXhx8=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V831rKT4rCpErJth1Z/6nQ2pjce3CEI7KGHIdbWXhx8="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overflow/FluentOverflow.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overflow\\FluentOverflow.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6575"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hVi+eZ1AhYzWA2HILBTSjl5xstub4DMGzUxGJIQgjVo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hVi+eZ1AhYzWA2HILBTSjl5xstub4DMGzUxGJIQgjVo="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overflow/FluentOverflow.razor.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\mm8wr5jb95-rgycuwl3sw.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000572082380"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1747"}, {"Name": "ETag", "Value": "\"fMg9R056t4+Y6PyTy3Auu6F5M2p0mm1ICT5RlLGEPQw=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"hVi+eZ1AhYzWA2HILBTSjl5xstub4DMGzUxGJIQgjVo=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hVi+eZ1AhYzWA2HILBTSjl5xstub4DMGzUxGJIQgjVo="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overflow/FluentOverflow.razor.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\mm8wr5jb95-rgycuwl3sw.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1747"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"fMg9R056t4+Y6PyTy3Auu6F5M2p0mm1ICT5RlLGEPQw=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fMg9R056t4+Y6PyTy3Auu6F5M2p0mm1ICT5RlLGEPQw="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overlay/FluentOverlay.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overlay\\FluentOverlay.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1977"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"IDySDi264SKaXFu1nL+hU2NeFhEMrX6Zv7ubUPR88VI=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IDySDi264SKaXFu1nL+hU2NeFhEMrX6Zv7ubUPR88VI="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overlay/FluentOverlay.razor.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\wmgko3ir4p-kjm33rwg1a.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001531393568"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "652"}, {"Name": "ETag", "Value": "\"Si6lAYTROr2Y2E+yQLNmoIuyol3tTTH2x0JT3bD9qzM=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"IDySDi264SKaXFu1nL+hU2NeFhEMrX6Zv7ubUPR88VI=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IDySDi264SKaXFu1nL+hU2NeFhEMrX6Zv7ubUPR88VI="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overlay/FluentOverlay.razor.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\wmgko3ir4p-kjm33rwg1a.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "652"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Si6lAYTROr2Y2E+yQLNmoIuyol3tTTH2x0JT3bD9qzM=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Si6lAYTROr2Y2E+yQLNmoIuyol3tTTH2x0JT3bD9qzM="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/PullToRefresh/FluentPullToRefresh.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\PullToRefresh\\FluentPullToRefresh.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6841"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xlA5fSAkA6TiFUznwHP835N8kAxJ7YJ5MTizYCGeOfo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xlA5fSAkA6TiFUznwHP835N8kAxJ7YJ5MTizYCGeOfo="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/PullToRefresh/FluentPullToRefresh.razor.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\jq2jihf5fr-awzanx0pu8.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000544365814"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1836"}, {"Name": "ETag", "Value": "\"gR+Q2B3kReBjCAmxaX0jrZ+LKn/mCFQH9svQins3Ffg=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"xlA5fSAkA6TiFUznwHP835N8kAxJ7YJ5MTizYCGeOfo=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xlA5fSAkA6TiFUznwHP835N8kAxJ7YJ5MTizYCGeOfo="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/PullToRefresh/FluentPullToRefresh.razor.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\jq2jihf5fr-awzanx0pu8.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1836"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gR+Q2B3kReBjCAmxaX0jrZ+LKn/mCFQH9svQins3Ffg=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gR+Q2B3kReBjCAmxaX0jrZ+LKn/mCFQH9svQins3Ffg="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Search/FluentSearch.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Search\\FluentSearch.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "917"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"FpN8ZcuZyVhdYb+cHNB4VZ5bLM+yi3gDaTZbWsahaYE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FpN8ZcuZyVhdYb+cHNB4VZ5bLM+yi3gDaTZbWsahaYE="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Search/FluentSearch.razor.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\uslingtyva-m0sdc2vg34.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002985074627"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "334"}, {"Name": "ETag", "Value": "\"eW7o1Dje91+P1j/ydSB4TyhEVA3pV/cMFp5ETKNpf94=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"FpN8ZcuZyVhdYb+cHNB4VZ5bLM+yi3gDaTZbWsahaYE=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FpN8ZcuZyVhdYb+cHNB4VZ5bLM+yi3gDaTZbWsahaYE="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Search/FluentSearch.razor.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\uslingtyva-m0sdc2vg34.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "334"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eW7o1Dje91+P1j/ydSB4TyhEVA3pV/cMFp5ETKNpf94=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eW7o1Dje91+P1j/ydSB4TyhEVA3pV/cMFp5ETKNpf94="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSlider.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSlider.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "445"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"TAnVg0aJviMtvE8pWYaaZahF5suJcjonGCC7accq76k=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TAnVg0aJviMtvE8pWYaaZahF5suJcjonGCC7accq76k="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSlider.razor.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\2b4v2q74ar-0b0bj86z40.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003496503497"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "285"}, {"Name": "ETag", "Value": "\"G3dG1kRqAVy5qnnnPHCI1eiXWJKlPgYyT43usniQ3po=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"TAnVg0aJviMtvE8pWYaaZahF5suJcjonGCC7accq76k=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TAnVg0aJviMtvE8pWYaaZahF5suJcjonGCC7accq76k="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSlider.razor.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\2b4v2q74ar-0b0bj86z40.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "285"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"G3dG1kRqAVy5qnnnPHCI1eiXWJKlPgYyT43usniQ3po=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-G3dG1kRqAVy5qnnnPHCI1eiXWJKlPgYyT43usniQ3po="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSliderLabel.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSliderLabel.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "340"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Em8bsrj69skLLR4IHVJ8lIJTR1EcY/U9nvcfn9t1rzo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Em8bsrj69skLLR4IHVJ8lIJTR1EcY/U9nvcfn9t1rzo="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSliderLabel.razor.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\5eye7ha8pe-e5lgg05xwp.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.004464285714"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "223"}, {"Name": "ETag", "Value": "\"ge8qfaevzXuPADoLXJRU/hwb7Kf7ZbPVfpfiy5/WzSE=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"Em8bsrj69skLLR4IHVJ8lIJTR1EcY/U9nvcfn9t1rzo=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Em8bsrj69skLLR4IHVJ8lIJTR1EcY/U9nvcfn9t1rzo="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSliderLabel.razor.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\5eye7ha8pe-e5lgg05xwp.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "223"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ge8qfaevzXuPADoLXJRU/hwb7Kf7ZbPVfpfiy5/WzSE=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ge8qfaevzXuPADoLXJRU/hwb7Kf7ZbPVfpfiy5/WzSE="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/SortableList/FluentSortableList.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\SortableList\\FluentSortableList.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1325"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rBxLYd0QGHwfD9IZljh74Lf+ZC+zqoRLqwikRKcRgpg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rBxLYd0QGHwfD9IZljh74Lf+ZC+zqoRLqwikRKcRgpg="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/SortableList/FluentSortableList.razor.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\fiqznfocyn-ki10xp5gks.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002114164905"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "472"}, {"Name": "ETag", "Value": "\"KrpRzB4c//N15dQfcbeepiBFyskX0d9lId+TSbEmr0g=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"rBxLYd0QGHwfD9IZljh74Lf+ZC+zqoRLqwikRKcRgpg=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rBxLYd0QGHwfD9IZljh74Lf+ZC+zqoRLqwikRKcRgpg="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/SortableList/FluentSortableList.razor.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\fiqznfocyn-ki10xp5gks.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "472"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KrpRzB4c//N15dQfcbeepiBFyskX0d9lId+TSbEmr0g=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KrpRzB4c//N15dQfcbeepiBFyskX0d9lId+TSbEmr0g="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Splitter/FluentMultiSplitter.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Splitter\\FluentMultiSplitter.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6140"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kExJSsKpmByqtTJ/TOwptCU5yawR+13aqkZxoVN+a1A=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kExJSsKpmByqtTJ/TOwptCU5yawR+13aqkZxoVN+a1A="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Splitter/FluentMultiSplitter.razor.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\5dcvuemm5a-s9hcthfn4x.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000745712155"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1340"}, {"Name": "ETag", "Value": "\"IrDP3f1jd+L0Llm1IWUdLxzfiqNeIJ3gein+ePzMnDw=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"kExJSsKpmByqtTJ/TOwptCU5yawR+13aqkZxoVN+a1A=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kExJSsKpmByqtTJ/TOwptCU5yawR+13aqkZxoVN+a1A="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Splitter/FluentMultiSplitter.razor.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\5dcvuemm5a-s9hcthfn4x.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1340"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"IrDP3f1jd+L0Llm1IWUdLxzfiqNeIJ3gein+ePzMnDw=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IrDP3f1jd+L0Llm1IWUdLxzfiqNeIJ3gein+ePzMnDw="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tabs/FluentTab.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tabs\\FluentTab.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "526"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Kh0YI9vhH0m+YJJvQVdOvtm0zuIIGEdRv3aH6iv7Gcg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Kh0YI9vhH0m+YJJvQVdOvtm0zuIIGEdRv3aH6iv7Gcg="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tabs/FluentTab.razor.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\sc7iw8bo2n-idf8r2y2gj.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003412969283"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "292"}, {"Name": "ETag", "Value": "\"TR1S8EwhHXGT0IDFQjqJEelFP9eV7MQuyLQoSh0H3hM=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"Kh0YI9vhH0m+YJJvQVdOvtm0zuIIGEdRv3aH6iv7Gcg=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Kh0YI9vhH0m+YJJvQVdOvtm0zuIIGEdRv3aH6iv7Gcg="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tabs/FluentTab.razor.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\sc7iw8bo2n-idf8r2y2gj.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "292"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"TR1S8EwhHXGT0IDFQjqJEelFP9eV7MQuyLQoSh0H3hM=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TR1S8EwhHXGT0IDFQjqJEelFP9eV7MQuyLQoSh0H3hM="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/TextField/FluentTextField.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\TextField\\FluentTextField.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1187"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YXiMRc9QPIiDSy+mlSF6DtYiSYb3X+1xlsCmrMrE2IU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YXiMRc9QPIiDSy+mlSF6DtYiSYb3X+1xlsCmrMrE2IU="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/TextField/FluentTextField.razor.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\sbcuig79xm-btwuipzwbp.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002150537634"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "464"}, {"Name": "ETag", "Value": "\"m+bgmT5d1joH0Fxa7CS+eJ+VwQ3mfXBcVitL9BGnJDA=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"YXiMRc9QPIiDSy+mlSF6DtYiSYb3X+1xlsCmrMrE2IU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YXiMRc9QPIiDSy+mlSF6DtYiSYb3X+1xlsCmrMrE2IU="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/TextField/FluentTextField.razor.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\sbcuig79xm-btwuipzwbp.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "464"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"m+bgmT5d1joH0Fxa7CS+eJ+VwQ3mfXBcVitL9BGnJDA=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m+bgmT5d1joH0Fxa7CS+eJ+VwQ3mfXBcVitL9BGnJDA="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Toolbar/FluentToolbar.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Toolbar\\FluentToolbar.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1364"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"s2w5uif33eV2OeQRoRzZYM1ANZXb6He68mkQ3IZw9Bc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-s2w5uif33eV2OeQRoRzZYM1ANZXb6He68mkQ3IZw9Bc="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Toolbar/FluentToolbar.razor.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\713i7dud0v-v95crb0bvb.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001953125000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "511"}, {"Name": "ETag", "Value": "\"tOg3Q58NSmbnSFU3YAHG8lAku8SwpggUpqTf+AnATaA=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"s2w5uif33eV2OeQRoRzZYM1ANZXb6He68mkQ3IZw9Bc=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-s2w5uif33eV2OeQRoRzZYM1ANZXb6He68mkQ3IZw9Bc="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Toolbar/FluentToolbar.razor.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\713i7dud0v-v95crb0bvb.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "511"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tOg3Q58NSmbnSFU3YAHG8lAku8SwpggUpqTf+AnATaA=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tOg3Q58NSmbnSFU3YAHG8lAku8SwpggUpqTf+AnATaA="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tooltip/FluentTooltip.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tooltip\\FluentTooltip.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "730"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pWY0aUTl5SagZBQwX/+DOHxke3fHSPoZdTQXbRQSFTU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pWY0aUTl5SagZBQwX/+DOHxke3fHSPoZdTQXbRQSFTU="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tooltip/FluentTooltip.razor.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\is0nail857-b0dyrub9as.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003571428571"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "279"}, {"Name": "ETag", "Value": "\"qYgukRZJexj+3utKj3bgZeh/3x4zsF0K/IgLGNtQZlU=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"pWY0aUTl5SagZBQwX/+DOHxke3fHSPoZdTQXbRQSFTU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pWY0aUTl5SagZBQwX/+DOHxke3fHSPoZdTQXbRQSFTU="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tooltip/FluentTooltip.razor.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\is0nail857-b0dyrub9as.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "279"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qYgukRZJexj+3utKj3bgZeh/3x4zsF0K/IgLGNtQZlU=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qYgukRZJexj+3utKj3bgZeh/3x4zsF0K/IgLGNtQZlU="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.1dlotxxwer.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\css\\reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7992"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2wyFQ9++b6uYwv3gv265xtRV2OWnPQMN68NpUHffScU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1dlotxxwer"}, {"Name": "integrity", "Value": "sha256-2wyFQ9++b6uYwv3gv265xtRV2OWnPQMN68NpUHffScU="}, {"Name": "label", "Value": "_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.css"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\css\\reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7992"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2wyFQ9++b6uYwv3gv265xtRV2OWnPQMN68NpUHffScU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2wyFQ9++b6uYwv3gv265xtRV2OWnPQMN68NpUHffScU="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.css", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\6nn42apl8y-1dlotxxwer.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000458295142"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2181"}, {"Name": "ETag", "Value": "\"yWJK/jJRjYMvTfcO31YHBmrdz81VC2EyYHDDmtydagw=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"2wyFQ9++b6uYwv3gv265xtRV2OWnPQMN68NpUHffScU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2wyFQ9++b6uYwv3gv265xtRV2OWnPQMN68NpUHffScU="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.css.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\6nn42apl8y-1dlotxxwer.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2181"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"yWJK/jJRjYMvTfcO31YHBmrdz81VC2EyYHDDmtydagw=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yWJK/jJRjYMvTfcO31YHBmrdz81VC2EyYHDDmtydagw="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/initializersLoader.webview.f8c5bd5212.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\initializersLoader.webview.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1121"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"L9w4Nw5htE5XBWcy0I11eRfWwkTxtN8VSJWnitKu30Q=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "f8c5bd5212"}, {"Name": "integrity", "Value": "sha256-L9w4Nw5htE5XBWcy0I11eRfWwkTxtN8VSJWnitKu30Q="}, {"Name": "label", "Value": "_content/Microsoft.FluentUI.AspNetCore.Components/js/initializersLoader.webview.js"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/initializersLoader.webview.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\initializersLoader.webview.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1121"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"L9w4Nw5htE5XBWcy0I11eRfWwkTxtN8VSJWnitKu30Q=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L9w4Nw5htE5XBWcy0I11eRfWwkTxtN8VSJWnitKu30Q="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/initializersLoader.webview.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\m1yjzwotev-f8c5bd5212.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001941747573"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "514"}, {"Name": "ETag", "Value": "\"whbSv+mD/ml9KCw7jXjGBPg1LWnmjRIlmI9T2b4UiTE=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"L9w4Nw5htE5XBWcy0I11eRfWwkTxtN8VSJWnitKu30Q=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L9w4Nw5htE5XBWcy0I11eRfWwkTxtN8VSJWnitKu30Q="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/initializersLoader.webview.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\m1yjzwotev-f8c5bd5212.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "514"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"whbSv+mD/ml9KCw7jXjGBPg1LWnmjRIlmI9T2b4UiTE=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-whbSv+mD/ml9KCw7jXjGBPg1LWnmjRIlmI9T2b4UiTE="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\loading-theme.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3190"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kX+9ky61TMxar94Z7+S8myontpvgH4571DVehjxVvM4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kX+9ky61TMxar94Z7+S8myontpvgH4571DVehjxVvM4="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\ruk4ibgzyp-t5s4sbrbsi.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000841042893"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1188"}, {"Name": "ETag", "Value": "\"P92YOWDKjxE0y0TlxQ45fEOnMJLoTqI3ePaSkqMS2GE=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"kX+9ky61TMxar94Z7+S8myontpvgH4571DVehjxVvM4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kX+9ky61TMxar94Z7+S8myontpvgH4571DVehjxVvM4="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\ruk4ibgzyp-t5s4sbrbsi.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1188"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"P92YOWDKjxE0y0TlxQ45fEOnMJLoTqI3ePaSkqMS2GE=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-P92YOWDKjxE0y0TlxQ45fEOnMJLoTqI3ePaSkqMS2GE="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.t5s4sbrbsi.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\loading-theme.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3190"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kX+9ky61TMxar94Z7+S8myontpvgH4571DVehjxVvM4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t5s4sbrbsi"}, {"Name": "integrity", "Value": "sha256-kX+9ky61TMxar94Z7+S8myontpvgH4571DVehjxVvM4="}, {"Name": "label", "Value": "_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.js"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.bundle.scp.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "101053"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"zuaQLy76kNrlxjFX+6hX984arQH48y6Q/ghVBSpwWT0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 13:05:28 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zuaQLy76kNrlxjFX+6hX984arQH48y6Q/ghVBSpwWT0="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.bundle.scp.css", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\5dr5gsymsw-q21vm7bk8w.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000072311809"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13828"}, {"Name": "ETag", "Value": "\"ilM3cnZ9w1Cds3H/szasZoTjgcOxMI6YHlcwRSDZ87w=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"zuaQLy76kNrlxjFX+6hX984arQH48y6Q/ghVBSpwWT0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zuaQLy76kNrlxjFX+6hX984arQH48y6Q/ghVBSpwWT0="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "392863"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"htNuF2gbem5vP85zcYVih92CTJrIeZj4ghrN/AHd9VM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 13:05:19 GMT"}], "EndpointProperties": [{"Name": "dependency-group", "Value": "js-initializer"}, {"Name": "integrity", "Value": "sha256-htNuF2gbem5vP85zcYVih92CTJrIeZj4ghrN/AHd9VM="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\kwrall9b0s-y92cxfqtgl.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010999406"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "90913"}, {"Name": "ETag", "Value": "\"ho7R4iSGK4SldCFlK5wThNxgWCzXFykru4YyKh0WJ5Y=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"htNuF2gbem5vP85zcYVih92CTJrIeZj4ghrN/AHd9VM=\""}], "EndpointProperties": [{"Name": "dependency-group", "Value": "js-initializer"}, {"Name": "integrity", "Value": "sha256-htNuF2gbem5vP85zcYVih92CTJrIeZj4ghrN/AHd9VM="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\kwrall9b0s-y92cxfqtgl.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "90913"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ho7R4iSGK4SldCFlK5wThNxgWCzXFykru4YyKh0WJ5Y=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ho7R4iSGK4SldCFlK5wThNxgWCzXFykru4YyKh0WJ5Y="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1022"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gD29yOMICDIiYM16Dl8m2EwS2lyds8DoFkgTy29qko4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 13:05:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gD29yOMICDIiYM16Dl8m2EwS2lyds8DoFkgTy29qko4="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\m3ffqup1h6-kz8gc8cxma.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001736111111"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "575"}, {"Name": "ETag", "Value": "\"2YmtOo2Bk8vpEVJoJ5uKDm6c1zUB6j043XC0D2H1AD0=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"gD29yOMICDIiYM16Dl8m2EwS2lyds8DoFkgTy29qko4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gD29yOMICDIiYM16Dl8m2EwS2lyds8DoFkgTy29qko4="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\m3ffqup1h6-kz8gc8cxma.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "575"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"2YmtOo2Bk8vpEVJoJ5uKDm6c1zUB6j043XC0D2H1AD0=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2YmtOo2Bk8vpEVJoJ5uKDm6c1zUB6j043XC0D2H1AD0="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1313081"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"VHCb/U7GL6QVbx5psaGUdfmCzhTRPWRUuIbJLkEigYo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 13:05:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VHCb/U7GL6QVbx5psaGUdfmCzhTRPWRUuIbJLkEigYo="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\3emkb5z5ma-8j22j5h3b2.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000003594498"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "278202"}, {"Name": "ETag", "Value": "\"oLgcOgt815aO1mw0Btae91K0hEcxrB2dJEZkTGZ996A=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"VHCb/U7GL6QVbx5psaGUdfmCzhTRPWRUuIbJLkEigYo=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VHCb/U7GL6QVbx5psaGUdfmCzhTRPWRUuIbJLkEigYo="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\3emkb5z5ma-8j22j5h3b2.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "278202"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"oLgcOgt815aO1mw0Btae91K0hEcxrB2dJEZkTGZ996A=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oLgcOgt815aO1mw0Btae91K0hEcxrB2dJEZkTGZ996A="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "101053"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"zuaQLy76kNrlxjFX+6hX984arQH48y6Q/ghVBSpwWT0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 13:05:28 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q21vm7bk8w"}, {"Name": "integrity", "Value": "sha256-zuaQLy76kNrlxjFX+6hX984arQH48y6Q/ghVBSpwWT0="}, {"Name": "label", "Value": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.bundle.scp.css"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\5dr5gsymsw-q21vm7bk8w.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13828"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ilM3cnZ9w1Cds3H/szasZoTjgcOxMI6YHlcwRSDZ87w=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ilM3cnZ9w1Cds3H/szasZoTjgcOxMI6YHlcwRSDZ87w="}]}, {"Route": "app.css", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\hpxq4k1yf6-kwazt7t2v0.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000419991600"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2380"}, {"Name": "ETag", "Value": "\"DVrr9NspEdIaqVmzWqlS/YCtNIAp20RehYwFf11WU9k=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A="}]}, {"Route": "app.css", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5583"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:34:33 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A="}]}, {"Route": "app.css.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\hpxq4k1yf6-kwazt7t2v0.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2380"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DVrr9NspEdIaqVmzWqlS/YCtNIAp20RehYwFf11WU9k=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DVrr9NspEdIaqVmzWqlS/YCtNIAp20RehYwFf11WU9k="}]}, {"Route": "app.kwazt7t2v0.css", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\hpxq4k1yf6-kwazt7t2v0.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000419991600"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2380"}, {"Name": "ETag", "Value": "\"DVrr9NspEdIaqVmzWqlS/YCtNIAp20RehYwFf11WU9k=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kwazt7t2v0"}, {"Name": "label", "Value": "app.css"}, {"Name": "integrity", "Value": "sha256-DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A="}]}, {"Route": "app.kwazt7t2v0.css", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5583"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:34:33 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kwazt7t2v0"}, {"Name": "label", "Value": "app.css"}, {"Name": "integrity", "Value": "sha256-DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A="}]}, {"Route": "app.kwazt7t2v0.css.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\hpxq4k1yf6-kwazt7t2v0.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2380"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DVrr9NspEdIaqVmzWqlS/YCtNIAp20RehYwFf11WU9k=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kwazt7t2v0"}, {"Name": "label", "Value": "app.css.gz"}, {"Name": "integrity", "Value": "sha256-DVrr9NspEdIaqVmzWqlS/YCtNIAp20RehYwFf11WU9k="}]}, {"Route": "CareerPortal.modules.json", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\2hecizl59g-uhfllo7vmv.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.010416666667"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "95"}, {"Name": "ETag", "Value": "\"tMpc4Mp5Kart9mS3UELWfInMDPFZYORmj0wiAw7JwBs=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "W/\"sgZOtYFoQbtT5NzEZj6mvNpqny3TPXduyD8nEg1wGig=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sgZOtYFoQbtT5NzEZj6mvNpqny3TPXduyD8nEg1wGig="}]}, {"Route": "CareerPortal.modules.json", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\jsmodules\\jsmodules.build.manifest.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "114"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"sgZOtYFoQbtT5NzEZj6mvNpqny3TPXduyD8nEg1wGig=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:55:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sgZOtYFoQbtT5NzEZj6mvNpqny3TPXduyD8nEg1wGig="}]}, {"Route": "CareerPortal.modules.json.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\2hecizl59g-uhfllo7vmv.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "95"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"tMpc4Mp5Kart9mS3UELWfInMDPFZYORmj0wiAw7JwBs=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tMpc4Mp5Kart9mS3UELWfInMDPFZYORmj0wiAw7JwBs="}]}, {"Route": "CareerPortal.styles.css", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\9w4jwsdqh9-tnv30r1bl8.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.008849557522"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "112"}, {"Name": "ETag", "Value": "\"Qjl948eSnEILonn3wELtC6BV9k8NVi6yotYo3mJkPSk=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk=\""}, {"Name": "Link", "Value": "<_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk="}]}, {"Route": "CareerPortal.styles.css", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\scopedcss\\bundle\\CareerPortal.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "131"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:55:26 GMT"}, {"Name": "Link", "Value": "<_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk="}]}, {"Route": "CareerPortal.styles.css.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\9w4jwsdqh9-tnv30r1bl8.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "112"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Qjl948eSnEILonn3wELtC6BV9k8NVi6yotYo3mJkPSk=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Qjl948eSnEILonn3wELtC6BV9k8NVi6yotYo3mJkPSk="}]}, {"Route": "CareerPortal.tnv30r1bl8.styles.css", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\9w4jwsdqh9-tnv30r1bl8.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.008849557522"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "112"}, {"Name": "ETag", "Value": "\"Qjl948eSnEILonn3wELtC6BV9k8NVi6yotYo3mJkPSk=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk=\""}, {"Name": "Link", "Value": "<_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tnv30r1bl8"}, {"Name": "integrity", "Value": "sha256-Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk="}, {"Name": "label", "Value": "CareerPortal.styles.css"}]}, {"Route": "CareerPortal.tnv30r1bl8.styles.css", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\scopedcss\\bundle\\CareerPortal.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "131"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:55:26 GMT"}, {"Name": "Link", "Value": "<_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tnv30r1bl8"}, {"Name": "integrity", "Value": "sha256-Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk="}, {"Name": "label", "Value": "CareerPortal.styles.css"}]}, {"Route": "CareerPortal.tnv30r1bl8.styles.css.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\9w4jwsdqh9-tnv30r1bl8.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "112"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Qjl948eSnEILonn3wELtC6BV9k8NVi6yotYo3mJkPSk=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tnv30r1bl8"}, {"Name": "label", "Value": "CareerPortal.styles.css.gz"}, {"Name": "integrity", "Value": "sha256-Qjl948eSnEILonn3wELtC6BV9k8NVi6yotYo3mJkPSk="}]}, {"Route": "css/landing.css", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\8nrps8m2up-vzu5x4vng6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000504286435"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1982"}, {"Name": "ETag", "Value": "\"Vm99OlISLLHxLmrZsC9T6bFROIQ+p9Ppt9xEqOgt0d8=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 09:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"1dLgneFV9PDKZ94smr3NcKjcLGsRYSEpjirulYCmaPw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1dLgneFV9PDKZ94smr3NcKjcLGsRYSEpjirulYCmaPw="}]}, {"Route": "css/landing.css", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\css\\landing.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7997"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1dLgneFV9PDKZ94smr3NcKjcLGsRYSEpjirulYCmaPw=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 09:36:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1dLgneFV9PDKZ94smr3NcKjcLGsRYSEpjirulYCmaPw="}]}, {"Route": "css/landing.css.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\8nrps8m2up-vzu5x4vng6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1982"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Vm99OlISLLHxLmrZsC9T6bFROIQ+p9Ppt9xEqOgt0d8=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 09:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Vm99OlISLLHxLmrZsC9T6bFROIQ+p9Ppt9xEqOgt0d8="}]}, {"Route": "css/landing.vzu5x4vng6.css", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\8nrps8m2up-vzu5x4vng6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000504286435"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1982"}, {"Name": "ETag", "Value": "\"Vm99OlISLLHxLmrZsC9T6bFROIQ+p9Ppt9xEqOgt0d8=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 09:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"1dLgneFV9PDKZ94smr3NcKjcLGsRYSEpjirulYCmaPw=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vzu5x4vng6"}, {"Name": "label", "Value": "css/landing.css"}, {"Name": "integrity", "Value": "sha256-1dLgneFV9PDKZ94smr3NcKjcLGsRYSEpjirulYCmaPw="}]}, {"Route": "css/landing.vzu5x4vng6.css", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\css\\landing.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7997"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1dLgneFV9PDKZ94smr3NcKjcLGsRYSEpjirulYCmaPw=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 09:36:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vzu5x4vng6"}, {"Name": "label", "Value": "css/landing.css"}, {"Name": "integrity", "Value": "sha256-1dLgneFV9PDKZ94smr3NcKjcLGsRYSEpjirulYCmaPw="}]}, {"Route": "css/landing.vzu5x4vng6.css.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\8nrps8m2up-vzu5x4vng6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1982"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Vm99OlISLLHxLmrZsC9T6bFROIQ+p9Ppt9xEqOgt0d8=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 09:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vzu5x4vng6"}, {"Name": "label", "Value": "css/landing.css.gz"}, {"Name": "integrity", "Value": "sha256-Vm99OlISLLHxLmrZsC9T6bFROIQ+p9Ppt9xEqOgt0d8="}]}, {"Route": "favicon.a8m5cweeeb.ico", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\2qd731de3k-a8m5cweeeb.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000182415177"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5481"}, {"Name": "ETag", "Value": "\"P6HBbEJ0eZi/m1PmO8SAHdJL1XHEjrq2+bDgcIheHt0=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "W/\"ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a8m5cweeeb"}, {"Name": "label", "Value": "favicon.ico"}, {"Name": "integrity", "Value": "sha256-ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs="}]}, {"Route": "favicon.a8m5cweeeb.ico", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15086"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:34:33 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a8m5cweeeb"}, {"Name": "label", "Value": "favicon.ico"}, {"Name": "integrity", "Value": "sha256-ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs="}]}, {"Route": "favicon.a8m5cweeeb.ico.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\2qd731de3k-a8m5cweeeb.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5481"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"P6HBbEJ0eZi/m1PmO8SAHdJL1XHEjrq2+bDgcIheHt0=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a8m5cweeeb"}, {"Name": "label", "Value": "favicon.ico.gz"}, {"Name": "integrity", "Value": "sha256-P6HBbEJ0eZi/m1PmO8SAHdJL1XHEjrq2+bDgcIheHt0="}]}, {"Route": "favicon.ico", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\2qd731de3k-a8m5cweeeb.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000182415177"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5481"}, {"Name": "ETag", "Value": "\"P6HBbEJ0eZi/m1PmO8SAHdJL1XHEjrq2+bDgcIheHt0=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "W/\"ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs="}]}, {"Route": "favicon.ico", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15086"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:34:33 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs="}]}, {"Route": "favicon.ico.gz", "AssetFile": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\2qd731de3k-a8m5cweeeb.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5481"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"P6HBbEJ0eZi/m1PmO8SAHdJL1XHEjrq2+bDgcIheHt0=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:56:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-P6HBbEJ0eZi/m1PmO8SAHdJL1XHEjrq2+bDgcIheHt0="}]}]}