/* Pak Suzuki Career Portal Landing Page Styles */

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

.landing-page {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
    padding: 80px 20px;
    text-align: center;
    position: relative;
    min-height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(255, 107, 53, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.05) 50%, transparent 70%);
    z-index: 1;
    animation: heroAnimation 20s ease-in-out infinite;
}

@keyframes heroAnimation {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.6; }
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
}

.hero h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero p {
    font-size: 1.3rem;
    margin-bottom: 30px;
    opacity: 0.95;
}

.cta-button {
    display: inline-block;
    background: #ff6b35;
    color: white;
    padding: 15px 35px;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.cta-button:hover {
    background: #e55a2b;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Section styles */
.section {
    padding: 60px 0;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    color: #1e3c72;
}

.section-subtitle {
    text-align: center;
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 50px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Program sections */
.program-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.program-card {
    background: white;
    border-radius: 15px;
    padding: 40px 30px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.6s ease forwards;
}

.program-card:nth-child(1) { animation-delay: 0.1s; }
.program-card:nth-child(2) { animation-delay: 0.2s; }
.program-card:nth-child(3) { animation-delay: 0.3s; }

.program-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.program-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    font-size: 2rem;
    color: white;
}

.program-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #1e3c72;
}

.program-card p {
    color: #666;
    margin-bottom: 25px;
    line-height: 1.6;
}

.learn-more {
    color: #1e3c72;
    text-decoration: none;
    font-weight: 600;
    border-bottom: 2px solid transparent;
    transition: border-color 0.3s ease;
}

.learn-more:hover {
    border-bottom-color: #1e3c72;
}

/* Recent jobs section */
.jobs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-top: 40px;
}

.job-card {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    border-left: 4px solid #ff6b35;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.5s ease forwards;
}

.job-card:nth-child(1) { animation-delay: 0.1s; }
.job-card:nth-child(2) { animation-delay: 0.2s; }
.job-card:nth-child(3) { animation-delay: 0.3s; }

.job-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.12);
}

.job-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #1e3c72;
    margin-bottom: 10px;
}

.job-meta {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    font-size: 0.9rem;
    color: #666;
}

.job-description {
    color: #555;
    margin-bottom: 20px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.apply-button {
    background: #1e3c72;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    text-decoration: none;
    display: inline-block;
    font-weight: 500;
    transition: background 0.3s ease;
}

.apply-button:hover {
    background: #2a5298;
}

/* Life at Pak Suzuki section */
.life-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.feature-item {
    text-align: center;
    padding: 20px;
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: #ff6b35;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 1.5rem;
}

.feature-item h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: #1e3c72;
}

.feature-item p {
    color: #666;
    font-size: 0.95rem;
}

/* Footer */
.footer {
    background: #1e3c72;
    color: white;
    padding: 50px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 30px;
}

.footer-section h4 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: #ff6b35;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul li a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: white;
}

.footer-bottom {
    border-top: 1px solid #2a5298;
    padding-top: 20px;
    text-align: center;
    color: #ccc;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .hero {
        padding: 60px 15px;
        min-height: 400px;
    }
    
    .hero h1 {
        font-size: 2.5rem;
    }
    
    .hero p {
        font-size: 1.1rem;
    }
    
    .section {
        padding: 40px 0;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .program-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .program-card {
        padding: 30px 20px;
    }
    
    .jobs-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .job-meta {
        flex-direction: column;
        gap: 5px;
    }
    
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .container {
        padding: 0 15px;
    }
}

@media (max-width: 480px) {
    .hero h1 {
        font-size: 2rem;
    }
    
    .section-title {
        font-size: 1.8rem;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .program-card {
        padding: 25px 15px;
    }
    
    .job-card {
        padding: 20px;
    }
}
