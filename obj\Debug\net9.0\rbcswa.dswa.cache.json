{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["BURBjdHREFeSc3o53xLBMnMgHeAlLAbxH0kmoO2OnXY=", "RsRQaFGY26JIivhPjpWXQ+a64hbXzXRHSy/0J8NmxNc=", "YZYEJhEs5lFzyBAyxGV3X9YzM0I5JJTY/nTejbzTP5w=", "AJhvEA8TgLS31j7ywwrpxcJn7U6yRinXQP3Xar8eCs0=", "rr6WOnz9eBqL2Slcyv7ilvIiRe+WB2yA5KSPXPq/DPc=", "6IrP5w0GgkoHGGtK4L9M0WrvPapqGkTU3CEoeRjpTao=", "FT8clA7Bhfjc7dUnmEPAu1vHVSPKDYT5ODGHOvBXwes=", "GD3Zm/6eeg/dvo7fjc0PA/7odbEHokIrhUvCWkDBMZs=", "LAfQr0+eR/KcaGxXbRrh69ezxVTi59jxFUBYvgV5aUY=", "uSquq087cND6P8Whb9MWV8T+LfxYHba6DaptIv4I59M=", "+fJLTSEma00O5KtxEgxz4fB9PVx95rQ8MjhyhsNyLtU=", "KHvvP/wB6fZzZUUASrvx74PLPq2EFqJyNZ8MnXaBM6c=", "Tjkro47IQWfQMdIIT6TI/rWiMNukIXt7jiwDc7qeSxY=", "+5G2VKmmglnAVhhxQpzd5K9K7+9NeyPgAwmWayNq0Fk=", "9QqugWK00sWBDFh4cs3RS51bg/RCntnDomkYq2LsRgQ=", "PFPeoyVpSUSNe/IaNntvLdTPjdbBwLGjvr6Cea82ts0=", "RvJ8sozUzfusqj4+jsb8U+f4ZTWe9jvXzGWmTbVQKTA=", "vewrSOMf7mlJImnKUsLVXIgIukCZl6w/4NcO2paiWrQ=", "tXjQCBtdRBReYv92YzCJUcTx3VzLZ1XGIssML/cPybk=", "xgBcOJrKxVQJsFmSEhHeSNwXUptwFkpheJGWxtjj2Nw=", "NKXz8qeBAzSNrDuzrroTtbg7NHGIaK80BJsBeOQj8N4=", "rY9H/5+L/MYW7+fqSNuGy1fyxqlmy6jrUQBz0MGT8Sg=", "T9RdRMxvwCO5kAG77HAyaLs2FCOV7Ll+vcrUKAwFeRk=", "ka558/xBl64MAQh1daKZhpqa11CArVsJfh1gKsuBFk4=", "zctLJndYZYt4AKT9E2nH5lF9WhXQ5AjBSEEbBLAUROk=", "vXhWIfxNAJTv6bHzDEuSPpK+EKHlcz7ZjB6nSDNHw2w=", "Ywnf2YQLsMQOyIRiuOwPkwIDYeczJ+Cvrs71JpT7imk=", "9iHrZgzXIF+PZJ/sbimWwB4kTfd+RYZJVvmlZxrs8VI=", "OsHFRrBFbiMyseszli8uJ7V2Uq62WUa4bBbCoS+AaDs=", "DA54K3ikqJ7rdVUGg7FPwSOR8mg6IA5u+NRjhUX8B0c=", "O5lv4xa4HIKr38RT+tvGlOvwvZJkQCTx1k7rDsAJ/m0=", "hj8Hbs6ZVHEQGS09lJGDZcYjouH/Vupy1nAJuTu3ffE=", "tIIZ+t6rBjY3+LfvLNqweaw2z9MDwIdEF0WDTpt8Wiw=", "bR2hCWaFIHH/sp4jbnP4X7iwUrhqcgzm3xm2AFYjZss=", "tz/iZhRnytXwjsLWP2MokFBPdioqkPSfaDworpjbLEk=", "TZfRtpqBybm0QboBsbrtcriblM/kECQ1udDnCfAGZqI=", "4Vh5Y5rWF95HG50PuDMKO63KKJKi4YJFscscas0UhoE=", "pj00yFfP+6lXLLe4BoRR3j26FmvcRbt/SePnpqSLiSg=", "cpMTA2Fh8Fw8d9R0d7SnHKgf5vfz5N0slZRuwvSLD+k=", "IjVc4o6tvbSF1p//Xi/s7DeYvKnK5uoTYIr2ZQIwL/8=", "85zC297LPexcQqTgyYEQV9j9QuG9ta20ZukfO4L73ss="], "CachedAssets": {"BURBjdHREFeSc3o53xLBMnMgHeAlLAbxH0kmoO2OnXY=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\cdrmjjq1d6-dpoev2zj9b.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Anchor/FluentAnchor.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Anchor\\FluentAnchor.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7c3acd42hn", "Integrity": "mTB1BoNq7t/us6gvhhRKI480CaK7vT9E+t+AL9an8wU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Anchor\\FluentAnchor.razor.js", "FileLength": 267, "LastWriteTime": "2025-05-31T05:56:09.0768137+00:00"}, "RsRQaFGY26JIivhPjpWXQ+a64hbXzXRHSy/0J8NmxNc=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\twd6d0wh4o-819w3ybe2d.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/AnchoredRegion/FluentAnchoredRegion.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\AnchoredRegion\\FluentAnchoredRegion.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0hkgbsy7ft", "Integrity": "pIZWamBcM95RyKOecGwL1QrEdg/IKc5rscB4MFCIujM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\AnchoredRegion\\FluentAnchoredRegion.razor.js", "FileLength": 996, "LastWriteTime": "2025-05-31T05:56:09.1193068+00:00"}, "YZYEJhEs5lFzyBAyxGV3X9YzM0I5JJTY/nTejbzTP5w=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\akzl5bxou0-p6kf5zqzit.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Button/FluentButton.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Button\\FluentButton.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h1hghvnkss", "Integrity": "RbdcZZJvJ98rtWAD8Wl13T6Jd/A0WUJIyozbvbTNME8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Button\\FluentButton.razor.js", "FileLength": 315, "LastWriteTime": "2025-05-31T05:56:09.0908276+00:00"}, "AJhvEA8TgLS31j7ywwrpxcJn7U6yRinXQP3Xar8eCs0=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\cq4bmj8gmb-zjzit57lox.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Checkbox/FluentCheckbox.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Checkbox\\FluentCheckbox.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3qb8k9lguq", "Integrity": "pWpjnsF9tEf741/+XNZJIUp59j8Fqiqzv5qPR9T/F/k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Checkbox\\FluentCheckbox.razor.js", "FileLength": 224, "LastWriteTime": "2025-05-31T05:56:09.0948553+00:00"}, "rr6WOnz9eBqL2Slcyv7ilvIiRe+WB2yA5KSPXPq/DPc=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\klh9ieqob8-nfhyg6xvey.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/DataGrid/FluentDataGrid.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DataGrid\\FluentDataGrid.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1kw23c60ji", "Integrity": "UQRUr2KMtLUqt9ogdiIWMXFjGLFL3UBvDZCD4pGFuKQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DataGrid\\FluentDataGrid.razor.js", "FileLength": 2941, "LastWriteTime": "2025-05-31T05:56:09.1180829+00:00"}, "6IrP5w0GgkoHGGtK4L9M0WrvPapqGkTU3CEoeRjpTao=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\4jvn6qpxj5-vyjqmndgy2.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/DesignSystemProvider/FluentDesignTheme.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DesignSystemProvider\\FluentDesignTheme.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fyjiiw4wfc", "Integrity": "ebidgEF0qxv0FVWWHIu9oAwtiTaKJTgfFFW7oNulicU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DesignSystemProvider\\FluentDesignTheme.razor.js", "FileLength": 751, "LastWriteTime": "2025-05-31T05:56:09.1193068+00:00"}, "FT8clA7Bhfjc7dUnmEPAu1vHVSPKDYT5ODGHOvBXwes=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\7g337ulk4n-iy34mpf72d.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Divider/FluentDivider.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Divider\\FluentDivider.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1z47admwto", "Integrity": "E+0P+4Q5g9SzSGdE20j3R0nckgPdNoGc0x+TGJ/TIYI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Divider\\FluentDivider.razor.js", "FileLength": 233, "LastWriteTime": "2025-05-31T05:56:09.1272128+00:00"}, "GD3Zm/6eeg/dvo7fjc0PA/7odbEHokIrhUvCWkDBMZs=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\6mwvvlkmgj-hi1gwvth64.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Grid/FluentGrid.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Grid\\FluentGrid.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jk2pvfrq94", "Integrity": "QySeeEbzUPczOjYjev2fIc86i78Pib7zlTPyfCkLv+8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Grid\\FluentGrid.razor.js", "FileLength": 756, "LastWriteTime": "2025-05-31T05:56:09.1635962+00:00"}, "LAfQr0+eR/KcaGxXbRrh69ezxVTi59jxFUBYvgV5aUY=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\pfc9to5k3q-5pcucyxosc.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/HorizontalScroll/FluentHorizontalScroll.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\HorizontalScroll\\FluentHorizontalScroll.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u6v528bpot", "Integrity": "2oCN2IEWRZPjloAzwScHmDLBlebfEgP366812p/E2c8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\HorizontalScroll\\FluentHorizontalScroll.razor.js", "FileLength": 141, "LastWriteTime": "2025-05-31T05:56:09.1656055+00:00"}, "uSquq087cND6P8Whb9MWV8T+LfxYHba6DaptIv4I59M=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\h0qxm5u0yy-vjluklws0l.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/InputFile/FluentInputFile.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\InputFile\\FluentInputFile.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "je4eg9uejw", "Integrity": "y0GyYNSHHrdFmkoHrD7jQi0oehiE/vh7Pi7iAEYeorU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\InputFile\\FluentInputFile.razor.js", "FileLength": 833, "LastWriteTime": "2025-05-31T05:56:09.1676192+00:00"}, "+fJLTSEma00O5KtxEgxz4fB9PVx95rQ8MjhyhsNyLtU=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\win2gt64sq-pu9hn1jugj.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/KeyCode/FluentKeyCode.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\KeyCode\\FluentKeyCode.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5y418xaf05", "Integrity": "TVs9bq3kylVYMAlXR3Kt4NTTdLquoTIUYrtItz80G30=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\KeyCode\\FluentKeyCode.razor.js", "FileLength": 888, "LastWriteTime": "2025-05-31T05:56:09.1143234+00:00"}, "KHvvP/wB6fZzZUUASrvx74PLPq2EFqJyNZ8MnXaBM6c=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\ijuf810ucb-xp2f0e0rh3.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Label/FluentInputLabel.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Label\\FluentInputLabel.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5oe6m2x2uv", "Integrity": "xT1lGfZ95NQfS99lKqhohgyvrwO4H0pOZ0PZuIj2+Xo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Label\\FluentInputLabel.razor.js", "FileLength": 253, "LastWriteTime": "2025-05-31T05:56:09.1272128+00:00"}, "Tjkro47IQWfQMdIIT6TI/rWiMNukIXt7jiwDc7qeSxY=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\6o9gthdpke-psptt994gq.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/List/FluentAutocomplete.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentAutocomplete.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2vkc6rmnnw", "Integrity": "YtlDojVlcrK2+2QBRP4LPueVRl8gCsL6Ez4wknxUJLQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentAutocomplete.razor.js", "FileLength": 571, "LastWriteTime": "2025-05-31T05:56:09.1696317+00:00"}, "+5G2VKmmglnAVhhxQpzd5K9K7+9NeyPgAwmWayNq0Fk=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\tb98bamshq-afevzs963z.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/List/FluentCombobox.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentCombobox.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f8na2y7sdz", "Integrity": "4fE5RW6001fSJc6+Ju/+RSpNSo5ZsvCuJri+aL4wof8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentCombobox.razor.js", "FileLength": 541, "LastWriteTime": "2025-05-31T05:56:09.1292971+00:00"}, "9QqugWK00sWBDFh4cs3RS51bg/RCntnDomkYq2LsRgQ=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\ffdjjohsnc-mmp1yy7un5.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/List/ListComponentBase.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\ListComponentBase.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0g1tupjq4p", "Integrity": "kD64FhYDGF1SA7wVn4LL4nX1pTWXmxuMYqo2xXip44E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\ListComponentBase.razor.js", "FileLength": 137, "LastWriteTime": "2025-05-31T05:56:09.1353443+00:00"}, "PFPeoyVpSUSNe/IaNntvLdTPjdbBwLGjvr6Cea82ts0=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\pn46o4hdgm-5wrroj4j54.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Menu/FluentMenu.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Menu\\FluentMenu.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fn6hmwpiy", "Integrity": "8zrNWZqy7raoxnB2CdCgVMkZDFcG6ue/1K4DaARm2RI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Menu\\FluentMenu.razor.js", "FileLength": 298, "LastWriteTime": "2025-05-31T05:56:09.1143234+00:00"}, "RvJ8sozUzfusqj4+jsb8U+f4ZTWe9jvXzGWmTbVQKTA=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\4sjayj892b-9fmja7pljs.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/NavMenu/FluentNavMenu.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\NavMenu\\FluentNavMenu.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zhd8n844ok", "Integrity": "V831rKT4rCpErJth1Z/6nQ2pjce3CEI7KGHIdbWXhx8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\NavMenu\\FluentNavMenu.razor.js", "FileLength": 1016, "LastWriteTime": "2025-05-31T05:56:09.1261022+00:00"}, "vewrSOMf7mlJImnKUsLVXIgIukCZl6w/4NcO2paiWrQ=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\mm8wr5jb95-rgycuwl3sw.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Overflow/FluentOverflow.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overflow\\FluentOverflow.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4s5d9cwut2", "Integrity": "fMg9R056t4+Y6PyTy3Auu6F5M2p0mm1ICT5RlLGEPQw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overflow\\FluentOverflow.razor.js", "FileLength": 1747, "LastWriteTime": "2025-05-31T05:56:09.1403814+00:00"}, "tXjQCBtdRBReYv92YzCJUcTx3VzLZ1XGIssML/cPybk=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\wmgko3ir4p-kjm33rwg1a.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Overlay/FluentOverlay.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overlay\\FluentOverlay.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "azo1hjcky2", "Integrity": "Si6lAYTROr2Y2E+yQLNmoIuyol3tTTH2x0JT3bD9qzM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overlay\\FluentOverlay.razor.js", "FileLength": 652, "LastWriteTime": "2025-05-31T05:56:09.1421381+00:00"}, "xgBcOJrKxVQJsFmSEhHeSNwXUptwFkpheJGWxtjj2Nw=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\jq2jihf5fr-awzanx0pu8.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/PullToRefresh/FluentPullToRefresh.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\PullToRefresh\\FluentPullToRefresh.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5rv3cg16bo", "Integrity": "gR+Q2B3kReBjCAmxaX0jrZ+LKn/mCFQH9svQins3Ffg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\PullToRefresh\\FluentPullToRefresh.razor.js", "FileLength": 1836, "LastWriteTime": "2025-05-31T05:56:09.1656055+00:00"}, "NKXz8qeBAzSNrDuzrroTtbg7NHGIaK80BJsBeOQj8N4=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\uslingtyva-m0sdc2vg34.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Search/FluentSearch.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Search\\FluentSearch.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3dn9v1xa0n", "Integrity": "eW7o1Dje91+P1j/ydSB4TyhEVA3pV/cMFp5ETKNpf94=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Search\\FluentSearch.razor.js", "FileLength": 334, "LastWriteTime": "2025-05-31T05:56:09.1143234+00:00"}, "rY9H/5+L/MYW7+fqSNuGy1fyxqlmy6jrUQBz0MGT8Sg=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\2b4v2q74ar-0b0bj86z40.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Slider/FluentSlider.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSlider.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dix5wgjndd", "Integrity": "G3dG1kRqAVy5qnnnPHCI1eiXWJKlPgYyT43usniQ3po=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSlider.razor.js", "FileLength": 285, "LastWriteTime": "2025-05-31T05:56:09.1272128+00:00"}, "T9RdRMxvwCO5kAG77HAyaLs2FCOV7Ll+vcrUKAwFeRk=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\5eye7ha8pe-e5lgg05xwp.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Slider/FluentSliderLabel.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSliderLabel.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rdpoyavzo1", "Integrity": "ge8qfaevzXuPADoLXJRU/hwb7Kf7ZbPVfpfiy5/WzSE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSliderLabel.razor.js", "FileLength": 223, "LastWriteTime": "2025-05-31T05:56:09.1594975+00:00"}, "ka558/xBl64MAQh1daKZhpqa11CArVsJfh1gKsuBFk4=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\fiqznfocyn-ki10xp5gks.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/SortableList/FluentSortableList.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\SortableList\\FluentSortableList.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mhffyjj8te", "Integrity": "KrpRzB4c//N15dQfcbeepiBFyskX0d9lId+TSbEmr0g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\SortableList\\FluentSortableList.razor.js", "FileLength": 472, "LastWriteTime": "2025-05-31T05:56:09.1635962+00:00"}, "zctLJndYZYt4AKT9E2nH5lF9WhXQ5AjBSEEbBLAUROk=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\5dcvuemm5a-s9hcthfn4x.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Splitter/FluentMultiSplitter.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Splitter\\FluentMultiSplitter.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6y9yx2mqnc", "Integrity": "IrDP3f1jd+L0Llm1IWUdLxzfiqNeIJ3gein+ePzMnDw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Splitter\\FluentMultiSplitter.razor.js", "FileLength": 1340, "LastWriteTime": "2025-05-31T05:56:09.1696317+00:00"}, "vXhWIfxNAJTv6bHzDEuSPpK+EKHlcz7ZjB6nSDNHw2w=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\sc7iw8bo2n-idf8r2y2gj.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Tabs/FluentTab.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tabs\\FluentTab.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "razgc08jz5", "Integrity": "TR1S8EwhHXGT0IDFQjqJEelFP9eV7MQuyLQoSh0H3hM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tabs\\FluentTab.razor.js", "FileLength": 292, "LastWriteTime": "2025-05-31T05:56:09.0627842+00:00"}, "Ywnf2YQLsMQOyIRiuOwPkwIDYeczJ+Cvrs71JpT7imk=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\sbcuig79xm-btwuipzwbp.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/TextField/FluentTextField.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\TextField\\FluentTextField.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n5p97eiurb", "Integrity": "m+bgmT5d1joH0Fxa7CS+eJ+VwQ3mfXBcVitL9BGnJDA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\TextField\\FluentTextField.razor.js", "FileLength": 464, "LastWriteTime": "2025-05-31T05:56:09.1193068+00:00"}, "9iHrZgzXIF+PZJ/sbimWwB4kTfd+RYZJVvmlZxrs8VI=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\713i7dud0v-v95crb0bvb.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Toolbar/FluentToolbar.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Toolbar\\FluentToolbar.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gzh5leg0or", "Integrity": "tOg3Q58NSmbnSFU3YAHG8lAku8SwpggUpqTf+AnATaA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Toolbar\\FluentToolbar.razor.js", "FileLength": 511, "LastWriteTime": "2025-05-31T05:56:09.1313167+00:00"}, "OsHFRrBFbiMyseszli8uJ7V2Uq62WUa4bBbCoS+AaDs=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\is0nail857-b0dyrub9as.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Tooltip/FluentTooltip.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tooltip\\FluentTooltip.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n43ij1gg6a", "Integrity": "qYgukRZJexj+3utKj3bgZeh/3x4zsF0K/IgLGNtQZlU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tooltip\\FluentTooltip.razor.js", "FileLength": 279, "LastWriteTime": "2025-05-31T05:56:09.1656055+00:00"}, "DA54K3ikqJ7rdVUGg7FPwSOR8mg6IA5u+NRjhUX8B0c=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\6nn42apl8y-1dlotxxwer.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "css/reboot.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\css\\reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lx5bc53mj0", "Integrity": "yWJK/jJRjYMvTfcO31YHBmrdz81VC2EyYHDDmtydagw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\css\\reboot.css", "FileLength": 2181, "LastWriteTime": "2025-05-31T05:56:09.1676192+00:00"}, "O5lv4xa4HIKr38RT+tvGlOvwvZJkQCTx1k7rDsAJ/m0=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\m1yjzwotev-f8c5bd5212.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "js/initializersLoader.webview.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\initializersLoader.webview.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mz0b1j7sap", "Integrity": "whbSv+mD/ml9KCw7jXjGBPg1LWnmjRIlmI9T2b4UiTE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\initializersLoader.webview.js", "FileLength": 514, "LastWriteTime": "2025-05-31T05:56:09.1143234+00:00"}, "hj8Hbs6ZVHEQGS09lJGDZcYjouH/Vupy1nAJuTu3ffE=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\ruk4ibgzyp-t5s4sbrbsi.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "js/loading-theme.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\loading-theme.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vs7zrwl0nd", "Integrity": "P92YOWDKjxE0y0TlxQ45fEOnMJLoTqI3ePaSkqMS2GE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\loading-theme.js", "FileLength": 1188, "LastWriteTime": "2025-05-31T05:56:09.1261022+00:00"}, "tIIZ+t6rBjY3+LfvLNqweaw2z9MDwIdEF0WDTpt8Wiw=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\kwrall9b0s-y92cxfqtgl.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.lib.module.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mhsh70qfxk", "Integrity": "ho7R4iSGK4SldCFlK5wThNxgWCzXFykru4YyKh0WJ5Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "FileLength": 90913, "LastWriteTime": "2025-05-31T05:56:09.1635962+00:00"}, "bR2hCWaFIHH/sp4jbnP4X7iwUrhqcgzm3xm2AFYjZss=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\m3ffqup1h6-kz8gc8cxma.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3x8k8dzeqr", "Integrity": "2YmtOo2Bk8vpEVJoJ5uKDm6c1zUB6j043XC0D2H1AD0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "FileLength": 575, "LastWriteTime": "2025-05-31T05:56:09.1656055+00:00"}, "tz/iZhRnytXwjsLWP2MokFBPdioqkPSfaDworpjbLEk=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\3emkb5z5ma-8j22j5h3b2.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0sncgxxouj", "Integrity": "oLgcOgt815aO1mw0Btae91K0hEcxrB2dJEZkTGZ996A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "FileLength": 278202, "LastWriteTime": "2025-05-31T05:56:09.2781532+00:00"}, "TZfRtpqBybm0QboBsbrtcriblM/kECQ1udDnCfAGZqI=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\hpxq4k1yf6-kwazt7t2v0.gz", "SourceId": "CareerPort<PERSON>", "SourceType": "Discovered", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CareerPortal", "RelativePath": "app#[.{fingerprint=kwazt7t2v0}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9tr1n8k5z8", "Integrity": "DVrr9NspEdIaqVmzWqlS/YCtNIAp20RehYwFf11WU9k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\app.css", "FileLength": 2380, "LastWriteTime": "2025-05-31T05:56:09.0612195+00:00"}, "4Vh5Y5rWF95HG50PuDMKO63KKJKi4YJFscscas0UhoE=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\2qd731de3k-a8m5cweeeb.gz", "SourceId": "CareerPort<PERSON>", "SourceType": "Discovered", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CareerPortal", "RelativePath": "favicon#[.{fingerprint=a8m5cweeeb}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lv19d4ri1a", "Integrity": "P6HBbEJ0eZi/m1PmO8SAHdJL1XHEjrq2+bDgcIheHt0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\favicon.ico", "FileLength": 5481, "LastWriteTime": "2025-05-31T05:56:09.0659118+00:00"}, "cpMTA2Fh8Fw8d9R0d7SnHKgf5vfz5N0slZRuwvSLD+k=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\2hecizl59g-uhfllo7vmv.gz", "SourceId": "CareerPort<PERSON>", "SourceType": "Computed", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CareerPortal", "RelativePath": "CareerPortal.modules.json.gz", "AssetKind": "Build", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\jsmodules\\jsmodules.build.manifest.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wvxjj87u8r", "Integrity": "tMpc4Mp5Kart9mS3UELWfInMDPFZYORmj0wiAw7JwBs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\jsmodules\\jsmodules.build.manifest.json", "FileLength": 95, "LastWriteTime": "2025-05-31T05:56:09.0659118+00:00"}, "IjVc4o6tvbSF1p//Xi/s7DeYvKnK5uoTYIr2ZQIwL/8=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\5dr5gsymsw-q21vm7bk8w.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mcb4qwo5eu", "Integrity": "ilM3cnZ9w1Cds3H/szasZoTjgcOxMI6YHlcwRSDZ87w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "FileLength": 13828, "LastWriteTime": "2025-05-31T05:56:09.1193068+00:00"}, "85zC297LPexcQqTgyYEQV9j9QuG9ta20ZukfO4L73ss=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\9w4jwsdqh9-tnv30r1bl8.gz", "SourceId": "CareerPort<PERSON>", "SourceType": "Computed", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CareerPortal", "RelativePath": "CareerPortal#[.{fingerprint=tnv30r1bl8}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\scopedcss\\bundle\\CareerPortal.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "utdp7ws6s3", "Integrity": "Qjl948eSnEILonn3wELtC6BV9k8NVi6yotYo3mJkPSk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\scopedcss\\bundle\\CareerPortal.styles.css", "FileLength": 112, "LastWriteTime": "2025-05-31T05:56:09.1313167+00:00"}, "pj00yFfP+6lXLLe4BoRR3j26FmvcRbt/SePnpqSLiSg=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\8nrps8m2up-vzu5x4vng6.gz", "SourceId": "CareerPort<PERSON>", "SourceType": "Discovered", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CareerPortal", "RelativePath": "css/landing#[.{fingerprint=vzu5x4vng6}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\css\\landing.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "asj2wc7qd1", "Integrity": "Vm99OlISLLHxLmrZsC9T6bFROIQ+p9Ppt9xEqOgt0d8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\css\\landing.css", "FileLength": 1982, "LastWriteTime": "2025-05-31T09:36:49.7509926+00:00"}}, "CachedCopyCandidates": {}}