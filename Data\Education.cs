using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CareerPortal.Data
{
    public class Education
    {
        public int Id { get; set; }

        [Required]
        public int CandidateProfileId { get; set; }
        [ForeignKey("CandidateProfileId")]
        public CandidateProfile? CandidateProfile { get; set; }

        [Required]
        [StringLength(100)]
        public string? DegreeName { get; set; }

        [Required]
        [StringLength(100)]
        public string? InstitutionName { get; set; }

        public int? GraduationYear { get; set; }

        [StringLength(10)]
        public string? GradeOrGPA { get; set; }
    }
}