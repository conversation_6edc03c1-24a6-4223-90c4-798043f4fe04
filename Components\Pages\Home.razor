@page "/"

@using CareerPortal.Data
@using CareerPortal.Components.Layout
@using Microsoft.EntityFrameworkCore
@inject ApplicationDbContext DbContext

<PageTitle>Pak Suzuki Career Portal - Join Our Team</PageTitle>

<LandingPageLayout RecentJobs="@recentJobs" />

@code {
    private List<Vacancy> recentJobs = new();

    protected override async Task OnInitializedAsync()
    {
        // Get top 3 recent active jobs
        recentJobs = await DbContext.Vacancies
            .Where(v => v.IsActive)
            .OrderByDescending(v => v.PostedDate)
            .Take(3)
            .ToListAsync();
    }
}