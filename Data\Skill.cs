using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CareerPortal.Data
{
    public class Skill
    {
        public int Id { get; set; }

        [Required]
        public int CandidateProfileId { get; set; }
        [ForeignKey("CandidateProfileId")]
        public CandidateProfile? CandidateProfile { get; set; }

        [Required]
        [StringLength(100)]
        public string? SkillName { get; set; }

        // Optional: Proficiency level (e.g., <PERSON><PERSON>ner, Intermediate, Advanced)
        [StringLength(50)]
        public string? ProficiencyLevel { get; set; }
    }
}