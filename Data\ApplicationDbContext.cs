using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace CareerPortal.Data
{
    public class ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : IdentityDbContext<ApplicationUser>(options)
    {
        public DbSet<Vacancy> Vacancies { get; set; }
        public DbSet<CandidateProfile> CandidateProfiles { get; set; }
        public DbSet<Education> Educations { get; set; }
        public DbSet<Experience> Experiences { get; set; }
        public DbSet<TrainingCertification> TrainingCertifications { get; set; }
        public DbSet<Skill> Skills { get; set; }
        public DbSet<AchievementAward> AchievementAwards { get; set; }
        public DbSet<CoverLetter> CoverLetters { get; set; }
        public DbSet<JobAlertPreference> JobAlertPreferences { get; set; }
        public DbSet<CandidateApplication> CandidateApplications { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            // Configure relationships if needed, for example:
            builder.Entity<CandidateApplication>()
                .HasOne(ca => ca.CandidateProfile)
                .WithMany(cp => cp.Applications)
                .HasForeignKey(ca => ca.CandidateProfileId)
                .OnDelete(DeleteBehavior.Restrict); // Prevent cascade delete issues

            builder.Entity<CandidateApplication>()
                .HasOne(ca => ca.Vacancy)
                .WithMany(v => v.Applications)
                .HasForeignKey(ca => ca.VacancyId)
                .OnDelete(DeleteBehavior.Restrict); // Prevent cascade delete issues
        }
    }
}
